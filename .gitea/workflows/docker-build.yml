name: Docker Build and Push

on:
  push:
    branches:
      - main  # 当 main 分支有推送时触发
  release:
    types: [published]  # 当发布新版本时触发

jobs:
  build-and-push:
    runs-on: self-hosted  # 修改为自托管 Runner 标签
    steps:
      - name: Checkout code
        uses: actions/checkout@v4  # 检出代码

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3  # 配置 Docker Buildx

      - name: Login to Docker Hub
        uses: docker/login-action@v3  # 登录 Docker Hub
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5  # 构建并推送 Docker 镜像
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/gemini-x:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/gemini-x:${{ github.sha }}
