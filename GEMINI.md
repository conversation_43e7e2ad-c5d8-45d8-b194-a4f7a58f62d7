# Gemini Project: My Awesome App

This is a web application built with React and Vite.

## Tech Stack

- **Language:** TypeScript
- **Framework:** React
- **Build Tool:** Vite
- **Styling:** CSS Modules

## Key Commands

- **Install dependencies:** `npm install`
- **Run development server:** `npm run dev`
- **Run tests:** `npm run test`
- **Build for production:** `npm run build`
- **Lint files:** `npm run lint`

## Coding Style

- Please follow the existing coding style.
- Use functional components with Hook<PERSON>.
- All new components must have accompanying tests.
