name: 🐳 Simple Build and Package

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for the release'
        required: true
        default: 'latest'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: kubex
  
jobs:
  build:
    name: 🚀 Build and Package
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
      
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        
      - name: 🏷️ Extract metadata
        id: meta
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            TAG_NAME="${{ github.event.inputs.tag_name }}"
          else
            TAG_NAME=${GITHUB_REF#refs/tags/}
          fi
          
          echo "tag_name=${TAG_NAME}" >> $GITHUB_OUTPUT
          echo "image_tag=${REGISTRY}/${{ github.repository_owner }}/${IMAGE_NAME}:${TAG_NAME}" >> $GITHUB_OUTPUT
          echo "build_date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_OUTPUT
          echo "build_revision=${GITHUB_SHA::8}" >> $GITHUB_OUTPUT
          
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 🏗️ Build and push multi-arch image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ steps.meta.outputs.image_tag }}
            ${{ env.REGISTRY }}/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}:latest
          labels: |
            org.opencontainers.image.title=KubeX
            org.opencontainers.image.description=Enterprise Kubernetes Management Platform
            org.opencontainers.image.version=${{ steps.meta.outputs.tag_name }}
            org.opencontainers.image.created=${{ steps.meta.outputs.build_date }}
            org.opencontainers.image.revision=${{ steps.meta.outputs.build_revision }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: 📦 Build AMD64 image for export
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64
          push: false
          tags: kubex:${{ steps.meta.outputs.tag_name }}
          outputs: type=docker,dest=kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar
          cache-from: type=gha
          
      - name: 📦 Compress and hash AMD64 image
        run: |
          gzip kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar
          sha256sum kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz > kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256
          
      - name: 📋 Generate deployment files
        run: |
          mkdir -p deployment
          
          # Docker Compose文件
          cat > deployment/docker-compose.yml << EOF
          version: '3.8'
          
          services:
            kubex:
              image: ${{ steps.meta.outputs.image_tag }}
              container_name: kubex
              ports:
                - "8080:8080"
              restart: unless-stopped
              environment:
                - NODE_ENV=production
              healthcheck:
                test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
                interval: 30s
                timeout: 10s
                retries: 3
                start_period: 40s
          EOF
          
          # 部署脚本
          cat > deployment/deploy.sh << 'EOF'
          #!/bin/bash
          set -e
          
          echo "🚀 KubeX ${{ steps.meta.outputs.tag_name }} 部署脚本"
          echo "=================================="
          
          if ! command -v docker &> /dev/null; then
              echo "❌ Docker未安装，请先安装Docker"
              exit 1
          fi
          
          if [ -f "../kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz" ]; then
              echo "📦 加载Docker镜像..."
              docker load < ../kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
          else
              echo "❌ 找不到镜像文件"
              exit 1
          fi
          
          echo "🛑 停止现有容器..."
          docker-compose down || true
          
          echo "🚀 启动KubeX容器..."
          docker-compose up -d
          
          echo "⏳ 等待服务启动..."
          sleep 10
          
          if docker-compose ps | grep -q "Up"; then
              echo "✅ KubeX部署成功！"
              echo "🌐 访问地址: http://localhost:8080"
              echo "📊 健康检查: http://localhost:8080/health"
          else
              echo "❌ 部署失败，请检查日志"
              docker-compose logs
              exit 1
          fi
          EOF
          
          chmod +x deployment/deploy.sh
          
          # README文件
          cat > deployment/README.md << 'EOF'
          # KubeX ${{ steps.meta.outputs.tag_name }} 部署指南
          
          ## 🚀 快速部署
          
          1. 确保Docker已安装并运行
          2. 将镜像文件和deployment文件夹放在同一目录
          3. 进入deployment目录: `cd deployment`
          4. 运行部署脚本: `./deploy.sh`
          5. 访问应用: http://localhost:8080
          
          ## 📦 文件说明
          
          - `kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz` - Docker镜像文件
          - `docker-compose.yml` - Docker Compose配置
          - `deploy.sh` - 自动部署脚本
          - `*.sha256` - 校验和文件
          
          ## 🔧 手动部署
          
          ```bash
          # 加载镜像
          docker load < kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
          
          # 运行容器
          docker run -d --name kubex -p 8080:8080 --restart unless-stopped ${{ steps.meta.outputs.image_tag }}
          ```
          
          ## 📊 监控
          
          ```bash
          # 查看状态
          docker ps
          
          # 查看日志
          docker logs kubex -f
          
          # 健康检查
          curl http://localhost:8080/health
          ```
          EOF
          
      - name: 📤 Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: kubex-${{ steps.meta.outputs.tag_name }}-deployment-package
          path: |
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256
            deployment/
          retention-days: 90
          
      - name: 📋 Create Release
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.meta.outputs.tag_name }}
          name: "KubeX ${{ steps.meta.outputs.tag_name }}"
          body: |
            ## 🐳 KubeX ${{ steps.meta.outputs.tag_name }} - 容器化部署包
            
            ### 🚀 快速部署
            
            1. 下载 `kubex-${{ steps.meta.outputs.tag_name }}-deployment-package.zip`
            2. 解压到Linux服务器
            3. 运行 `cd deployment && ./deploy.sh`
            4. 访问 http://your-server:8080
            
            ### 📊 镜像信息
            
            - **镜像地址**: `${{ steps.meta.outputs.image_tag }}`
            - **支持架构**: linux/amd64, linux/arm64
            - **构建时间**: ${{ steps.meta.outputs.build_date }}
            
            ### 🐳 直接使用镜像
            
            ```bash
            docker pull ${{ steps.meta.outputs.image_tag }}
            docker run -d --name kubex -p 8080:8080 ${{ steps.meta.outputs.image_tag }}
            ```
            
          files: |
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256
            deployment/docker-compose.yml
            deployment/deploy.sh
            deployment/README.md
          draft: false
          prerelease: false
