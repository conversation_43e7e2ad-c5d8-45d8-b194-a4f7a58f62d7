name: 🐳 Build and Package KubeX Container (Disabled)

on:
  # push:
  #   tags:
  #     - 'v*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for the release'
        required: true
        default: 'latest'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: kubex
  
jobs:
  build-and-package:
    name: 🚀 Build Docker Image and Export TAR
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
      id-token: write
      
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        
      - name: 🏷️ Extract metadata
        id: meta
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            TAG_NAME="${{ github.event.inputs.tag_name }}"
          else
            TAG_NAME=${GITHUB_REF#refs/tags/}
          fi
          
          echo "tag_name=${TAG_NAME}" >> $GITHUB_OUTPUT
          echo "image_tag=${REGISTRY}/${{ github.repository_owner }}/${IMAGE_NAME}:${TAG_NAME}" >> $GITHUB_OUTPUT
          echo "image_tag_latest=${REGISTRY}/${{ github.repository_owner }}/${IMAGE_NAME}:latest" >> $GITHUB_OUTPUT
          
          # 生成构建时间戳
          echo "build_date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_OUTPUT
          echo "build_revision=${GITHUB_SHA::8}" >> $GITHUB_OUTPUT
          
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64
          
      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          logout: false
          
      - name: 🏗️ Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ steps.meta.outputs.image_tag }}
            ${{ steps.meta.outputs.image_tag_latest }}
          labels: |
            org.opencontainers.image.title=KubeX
            org.opencontainers.image.description=Enterprise Kubernetes Management Platform
            org.opencontainers.image.version=${{ steps.meta.outputs.tag_name }}
            org.opencontainers.image.created=${{ steps.meta.outputs.build_date }}
            org.opencontainers.image.revision=${{ steps.meta.outputs.build_revision }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.url=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.vendor=KubeX Team
            org.opencontainers.image.licenses=MIT
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: 📦 Build and Export Docker image for AMD64
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64
          push: false
          tags: kubex:${{ steps.meta.outputs.tag_name }}-amd64
          outputs: type=docker,dest=kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 📦 Compress and hash AMD64 image
        run: |
          # 压缩tar文件
          gzip kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar

          # 生成SHA256校验和
          sha256sum kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz > kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256

      - name: 📦 Build and Export Docker image for ARM64
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/arm64
          push: false
          tags: kubex:${{ steps.meta.outputs.tag_name }}-arm64
          outputs: type=docker,dest=kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 📦 Compress and hash ARM64 image
        run: |
          # 压缩tar文件
          gzip kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar

          # 生成SHA256校验和
          sha256sum kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz > kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz.sha256
          
      - name: 📋 Generate deployment files
        run: |
          # 创建部署目录
          mkdir -p deployment
          
          # 生成docker-compose.yml
          cat > deployment/docker-compose.yml << 'EOF'
          version: '3.8'
          
          services:
            kubex:
              image: ${{ steps.meta.outputs.image_tag }}
              container_name: kubex
              ports:
                - "8080:8080"
              restart: unless-stopped
              environment:
                - NODE_ENV=production
              healthcheck:
                test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
                interval: 30s
                timeout: 10s
                retries: 3
                start_period: 40s
              labels:
                - "traefik.enable=true"
                - "traefik.http.routers.kubex.rule=Host(\`kubex.local\`)"
                - "traefik.http.services.kubex.loadbalancer.server.port=8080"
          EOF
          
          # 生成部署脚本
          cat > deployment/deploy.sh << 'EOF'
          #!/bin/bash
          set -e
          
          echo "🚀 KubeX ${{ steps.meta.outputs.tag_name }} 部署脚本"
          echo "=================================="
          
          # 检查Docker是否安装
          if ! command -v docker &> /dev/null; then
              echo "❌ Docker未安装，请先安装Docker"
              exit 1
          fi
          
          # 加载镜像
          if [ -f "kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz" ]; then
              echo "📦 加载Docker镜像..."
              docker load < kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
          else
              echo "❌ 找不到镜像文件"
              exit 1
          fi
          
          # 停止现有容器
          echo "🛑 停止现有容器..."
          docker-compose down || true
          
          # 启动新容器
          echo "🚀 启动KubeX容器..."
          docker-compose up -d
          
          # 等待服务启动
          echo "⏳ 等待服务启动..."
          sleep 10
          
          # 检查服务状态
          if docker-compose ps | grep -q "Up"; then
              echo "✅ KubeX部署成功！"
              echo "🌐 访问地址: http://localhost:8080"
              echo "📊 健康检查: http://localhost:8080/health"
          else
              echo "❌ 部署失败，请检查日志"
              docker-compose logs
              exit 1
          fi
          EOF
          
          chmod +x deployment/deploy.sh
          
          # 生成README
          cat > deployment/README.md << 'EOF'
          # KubeX ${{ steps.meta.outputs.tag_name }} 部署指南
          
          ## 📦 包含文件
          
          - `kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz` - Docker镜像文件 (AMD64)
          - `kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz` - Docker镜像文件 (ARM64)
          - `docker-compose.yml` - Docker Compose配置
          - `deploy.sh` - 自动部署脚本
          - `*.sha256` - 校验和文件
          
          ## 🚀 快速部署
          
          ### 方法1: 使用部署脚本
          ```bash
          chmod +x deploy.sh
          ./deploy.sh
          ```
          
          ### 方法2: 手动部署
          ```bash
          # 1. 加载镜像
          docker load < kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
          
          # 2. 启动容器
          docker-compose up -d
          
          # 3. 检查状态
          docker-compose ps
          ```
          
          ### 方法3: 直接运行
          ```bash
          docker run -d \
            --name kubex \
            -p 8080:8080 \
            --restart unless-stopped \
            ${{ steps.meta.outputs.image_tag }}
          ```
          
          ## 🌐 访问应用
          
          - **主页**: http://localhost:8080
          - **健康检查**: http://localhost:8080/health
          
          ## 📊 监控和日志
          
          ```bash
          # 查看容器状态
          docker-compose ps
          
          # 查看日志
          docker-compose logs -f
          
          # 重启服务
          docker-compose restart
          
          # 停止服务
          docker-compose down
          ```
          
          ## 🔧 故障排除
          
          1. **端口冲突**: 修改docker-compose.yml中的端口映射
          2. **权限问题**: 确保当前用户在docker组中
          3. **镜像损坏**: 使用sha256校验和验证文件完整性
          
          ```bash
          # 验证文件完整性
          sha256sum -c kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256
          ```
          EOF

      - name: 📊 Generate build info
        run: |
          cat > build-info.json << EOF
          {
            "version": "${{ steps.meta.outputs.tag_name }}",
            "build_date": "${{ steps.meta.outputs.build_date }}",
            "build_revision": "${{ steps.meta.outputs.build_revision }}",
            "image_tag": "${{ steps.meta.outputs.image_tag }}",
            "platforms": ["linux/amd64", "linux/arm64"],
            "registry": "${{ env.REGISTRY }}",
            "repository": "${{ github.repository }}",
            "workflow_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          }
          EOF

      - name: 📤 Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: kubex-${{ steps.meta.outputs.tag_name }}-deployment-package
          path: |
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256
            kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz
            kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz.sha256
            deployment/
            build-info.json
          retention-days: 90

      - name: 📋 Create Release (if tag)
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.meta.outputs.tag_name }}
          name: "KubeX ${{ steps.meta.outputs.tag_name }}"
          body: |
            ## 🐳 KubeX ${{ steps.meta.outputs.tag_name }} - 容器化部署包

            ### 📦 部署包内容

            - **Docker镜像**: 支持 AMD64 和 ARM64 架构
            - **部署脚本**: 一键部署到Linux服务器
            - **Docker Compose**: 生产环境配置
            - **完整文档**: 部署和运维指南

            ### 🚀 快速部署

            1. 下载对应架构的部署包
            2. 解压到Linux服务器
            3. 运行 `./deploy.sh` 即可完成部署

            ### 📊 镜像信息

            - **镜像地址**: `${{ steps.meta.outputs.image_tag }}`
            - **构建时间**: ${{ steps.meta.outputs.build_date }}
            - **构建版本**: ${{ steps.meta.outputs.build_revision }}
            - **支持架构**: linux/amd64, linux/arm64

            ### 🌐 访问地址

            部署完成后访问: http://your-server:8080

            ### 📖 详细文档

            请查看部署包中的 `deployment/README.md` 文件获取详细部署说明。

          files: |
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz
            kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz.sha256
            kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz
            kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz.sha256
            deployment/docker-compose.yml
            deployment/deploy.sh
            deployment/README.md
            build-info.json
          draft: false
          prerelease: false

      - name: 📊 Build Summary
        run: |
          echo "## 🎉 构建完成!" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📦 构建信息" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: ${{ steps.meta.outputs.tag_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像**: ${{ steps.meta.outputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建时间**: ${{ steps.meta.outputs.build_date }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建版本**: ${{ steps.meta.outputs.build_revision }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🐳 Docker镜像" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`bash" >> $GITHUB_STEP_SUMMARY
          echo "docker pull ${{ steps.meta.outputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📁 部署文件" >> $GITHUB_STEP_SUMMARY
          echo "- AMD64镜像: \`kubex-${{ steps.meta.outputs.tag_name }}-linux-amd64.tar.gz\`" >> $GITHUB_STEP_SUMMARY
          echo "- ARM64镜像: \`kubex-${{ steps.meta.outputs.tag_name }}-linux-arm64.tar.gz\`" >> $GITHUB_STEP_SUMMARY
          echo "- 部署脚本: \`deployment/deploy.sh\`" >> $GITHUB_STEP_SUMMARY
          echo "- Docker Compose: \`deployment/docker-compose.yml\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🚀 快速部署" >> $GITHUB_STEP_SUMMARY
          echo "1. 下载部署包到Linux服务器" >> $GITHUB_STEP_SUMMARY
          echo "2. 解压: \`tar -xzf kubex-${{ steps.meta.outputs.tag_name }}-deployment-package.tar.gz\`" >> $GITHUB_STEP_SUMMARY
          echo "3. 部署: \`cd deployment && ./deploy.sh\`" >> $GITHUB_STEP_SUMMARY
          echo "4. 访问: http://your-server:8080" >> $GITHUB_STEP_SUMMARY
