name: Build and Export Image

on:
  push:
    branches: [ "main" ]

jobs:
  build-and-export:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run lint and tests
        run: |
          npm run lint
          npm run test
      
      - name: Build and export to Docker tar
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: gemini-x:latest
          # 明确指定为您的 Mac Intel 设备构建兼容的 amd64 架构
          platforms: linux/amd64
          # Export the image as a tar file
          outputs: type=docker,dest=/tmp/image.tar

      - name: Upload Docker image artifact
        uses: actions/upload-artifact@v4
        with:
          name: docker-image-amd64
          path: /tmp/image.tar