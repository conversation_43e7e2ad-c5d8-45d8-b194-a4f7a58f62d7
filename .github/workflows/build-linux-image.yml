name: Build Linux Docker Image

on:
  push:
    branches:
      - main  # 当 main 分支有推送时触发
  release:
    types: [published]  # 当发布新版本时触发

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64  # 构建多架构镜像
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/your-app-name:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/your-app-name:${{ github.sha }}
