import React, { useState, useEffect } from 'react';
import {
  <PERSON>ss<PERSON><PERSON><PERSON>,
  ThemeProvider,
  createTheme,
  AppBar,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Button,
  Box,
  Container,
  TextField,
  Paper,
  Alert
} from '@mui/material';
import {
  Dashboard,
  Business,
  People,
  Settings,
  Security,
  Logout,
  CloudUpload
} from '@mui/icons-material';
import { simpleAuthService } from './services/simpleAuth';
import PlatformOverview from './pages/PlatformOverview';
import WorkspaceOperationPanel from './pages/WorkspaceOperationPanel';
import SimpleWorkspaceManagement from './pages/SimpleWorkspaceManagement';
import SimpleUserManagement from './pages/SimpleUserManagement';
import ApplicationDeployment from './pages/ApplicationDeployment';
import SimpleDebugPage from './pages/SimpleDebugPage';
import ConfigDialog from './components/ConfigDialog';
import ErrorBoundary from './components/ErrorBoundary';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
    text: {
      primary: 'rgba(255, 255, 255, 0.87)',
      secondary: 'rgba(255, 255, 255, 0.6)',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica Neue", Arial, sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          borderRadius: 12,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#1e1e1e',
        },
      },
    },
  },
});

function App() {
  // 认证相关状态
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [currentPage, setCurrentPage] = useState('overview');
  const [message, setMessage] = useState('KubeX 管理平台正常运行');
  const [configDialogOpen, setConfigDialogOpen] = useState(false);

  useEffect(() => {
    // 检查认证状态
    const authState = simpleAuthService.getAuthState();
    setIsAuthenticated(authState.isAuthenticated);
    setCurrentUser(authState.user);
  }, []);

  const handleConfigSave = (config) => {
    localStorage.setItem('rancherConfig', JSON.stringify(config));
    setConfigDialogOpen(false);
    setMessage('Rancher 配置已保存，请刷新页面');
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    try {
      await simpleAuthService.login(loginForm.username, loginForm.password);
      const authState = simpleAuthService.getAuthState();
      setIsAuthenticated(true);
      setCurrentUser(authState.user);
      setMessage('登录成功！');
    } catch (error) {
      setMessage('登录失败: ' + error.message);
    }
  };

  const handleLogout = () => {
    simpleAuthService.logout();
    setIsAuthenticated(false);
    setCurrentUser(null);
    setMessage('已退出登录');
  };

  // 如果未认证，显示登录表单
  if (!isAuthenticated) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Container maxWidth="sm" sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4
        }}>
          <Box sx={{ width: '100%' }}>
            <Paper elevation={3} sx={{ p: 4, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                <Security sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
                <Typography variant="h4" component="h1">
                  KubeX 登录
                </Typography>
              </Box>

              {message !== 'KubeX 管理平台正常运行' && (
                <Alert severity={message.includes('成功') ? 'success' : 'error'} sx={{ mb: 3 }}>
                  {message}
                </Alert>
              )}

              <Box component="form" onSubmit={handleLogin}>
                <TextField
                  fullWidth
                  label="用户名"
                  variant="outlined"
                  margin="normal"
                  required
                  value={loginForm.username}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, username: e.target.value }))}
                />

                <TextField
                  fullWidth
                  label="密码"
                  type="password"
                  variant="outlined"
                  margin="normal"
                  required
                  value={loginForm.password}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  sx={{ mt: 3, mb: 2 }}
                >
                  登录
                </Button>
              </Box>
            </Paper>

            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                演示账户
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => setLoginForm({ username: 'admin', password: 'admin123' })}
                  fullWidth
                >
                  管理员登录 (admin / admin123)
                </Button>

                <Button
                  variant="outlined"
                  onClick={() => setLoginForm({ username: 'operator1', password: 'op123' })}
                  fullWidth
                >
                  操作员登录 (operator1 / op123)
                </Button>
              </Box>
            </Paper>
          </Box>
        </Container>
      </ThemeProvider>
    );
  }

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              KubeX 管理平台
            </Typography>

          <Button
            color="inherit"
            onClick={() => setCurrentPage('overview')}
            variant={currentPage === 'overview' ? 'outlined' : 'text'}
            startIcon={<Dashboard />}
            sx={{ mr: 1 }}
          >
            平台总览
          </Button>

          <Button
            color="inherit"
            onClick={() => setCurrentPage('dashboard')}
            variant={currentPage === 'dashboard' ? 'outlined' : 'text'}
            startIcon={<Settings />}
            sx={{ mr: 1 }}
          >
            工作区操作台
          </Button>

          <Button
            color="inherit"
            onClick={() => setCurrentPage('deployment')}
            variant={currentPage === 'deployment' ? 'outlined' : 'text'}
            startIcon={<CloudUpload />}
            sx={{ mr: 1 }}
          >
            应用部署
          </Button>

          {currentUser?.role === 'admin' && (
            <>
              <Button
                color="inherit"
                onClick={() => setCurrentPage('workspace')}
                variant={currentPage === 'workspace' ? 'outlined' : 'text'}
                startIcon={<Business />}
                sx={{ mr: 1 }}
              >
                工作区管理
              </Button>
              <Button
                color="inherit"
                onClick={() => setCurrentPage('users')}
                variant={currentPage === 'users' ? 'outlined' : 'text'}
                startIcon={<People />}
                sx={{ mr: 1 }}
              >
                用户管理
              </Button>
            </>
          )}

          <Button
            color="inherit"
            onClick={() => setCurrentPage('debug')}
            variant={currentPage === 'debug' ? 'outlined' : 'text'}
            startIcon={<Settings />}
            sx={{ mr: 2 }}
          >
            调试工具
          </Button>

          <Button
            color="inherit"
            onClick={handleLogout}
            variant="outlined"
            startIcon={<Logout />}
          >
            退出 ({currentUser?.username})
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 4 }}>
        {currentPage === 'overview' && <PlatformOverview />}

        {currentPage === 'dashboard' && <WorkspaceOperationPanel />}

        {currentPage === 'deployment' && <ApplicationDeployment />}

        {currentPage === 'workspace' && <SimpleWorkspaceManagement />}

        {currentPage === 'users' && <SimpleUserManagement />}

        {currentPage === 'debug' && <SimpleDebugPage />}
      </Container>

      {/* Rancher 配置对话框 */}
      <ConfigDialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        onSave={handleConfigSave}
        initialConfig={null}
      />
    </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
