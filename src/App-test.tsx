import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ThemeProvider,
  createTheme,
  AppBar,
  Tool<PERSON>,
  Typography,
  Button,
  Box,
  Container,
  Card,
  CardContent,
  Alert
} from '@mui/material';
import {
  Dashboard,
  Settings,
  CloudUpload
} from '@mui/icons-material';

const theme = createTheme({
  palette: {
    mode: 'dark',
  },
});

function TestApp() {
  const [currentPage, setCurrentPage] = useState('overview');

  const renderContent = () => {
    switch (currentPage) {
      case 'overview':
        return (
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                平台总览
              </Typography>
              <Alert severity="success">
                KubeX 管理平台运行正常
              </Alert>
            </CardContent>
          </Card>
        );
      case 'deployment':
        return (
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                应用部署
              </Typography>
              <Alert severity="info">
                容器部署功能开发中
              </Alert>
            </CardContent>
          </Card>
        );
      case 'debug':
        return (
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                调试工具
              </Typography>
              <Alert severity="info">
                系统运行正常
              </Alert>
            </CardContent>
          </Card>
        );
      default:
        return (
          <Card>
            <CardContent>
              <Typography variant="h5">
                欢迎使用 KubeX
              </Typography>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            KubeX 管理平台 (测试版)
          </Typography>

          <Button
            color="inherit"
            onClick={() => setCurrentPage('overview')}
            variant={currentPage === 'overview' ? 'outlined' : 'text'}
            startIcon={<Dashboard />}
            sx={{ mr: 1 }}
          >
            平台总览
          </Button>

          <Button
            color="inherit"
            onClick={() => setCurrentPage('deployment')}
            variant={currentPage === 'deployment' ? 'outlined' : 'text'}
            startIcon={<CloudUpload />}
            sx={{ mr: 1 }}
          >
            应用部署
          </Button>

          <Button
            color="inherit"
            onClick={() => setCurrentPage('debug')}
            variant={currentPage === 'debug' ? 'outlined' : 'text'}
            startIcon={<Settings />}
            sx={{ mr: 1 }}
          >
            调试工具
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {renderContent()}
      </Container>
    </ThemeProvider>
  );
}

export default TestApp;
