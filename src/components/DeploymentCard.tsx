
// src/components/DeploymentCard.tsx

import React, { useState } from 'react';
import { Card, CardContent, CardActions, Typography, Button, Chip, TextField, Box } from '@mui/material';
import type { Deployment } from '../services/rancherApi';

interface Props {
  deployment: Deployment;
  onScale: (namespace: string, name: string, replicas: number) => void;
  onRestart: (namespace: string, name: string) => void;
}

const DeploymentCard: React.FC<Props> = ({ deployment, onScale, onRestart }) => {
  const [replicas, setReplicas] = useState(deployment.replicas);

  const handleScaleClick = () => {
    onScale(deployment.namespace, deployment.name, replicas);
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="div" noWrap>
          {deployment.name}
        </Typography>
        <Typography color="text.secondary" gutterBottom>
          {deployment.namespace}
        </Typography>
        <Chip
          label={`${deployment.availableReplicas}/${deployment.replicas} Ready`}
          color={deployment.availableReplicas === deployment.replicas ? 'success' : 'warning'}
          size="small"
          sx={{ mb: 1 }}
        />
        <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
          Image: {deployment.image}
        </Typography>
      </CardContent>
      <CardActions>
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <TextField
            type="number"
            value={replicas}
            onChange={(e) => setReplicas(parseInt(e.target.value, 10))}
            size="small"
            sx={{ width: 80, mr: 1 }}
            inputProps={{ min: 0 }}
          />
          <Button size="small" onClick={handleScaleClick}>Scale</Button>
          <Button size="small" onClick={() => onRestart(deployment.namespace, deployment.name)} color="secondary">
            Restart
          </Button>
        </Box>
      </CardActions>
    </Card>
  );
};

export default DeploymentCard;
