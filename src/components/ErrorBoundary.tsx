import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Alert,
  Container
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  BugReport as BugIcon
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <Container maxWidth="md">
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            minHeight="100vh"
            py={4}
          >
            <Card sx={{ width: '100%', maxWidth: 600 }}>
              <CardContent sx={{ textAlign: 'center', p: 4 }}>
                <BugIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
                
                <Typography variant="h4" gutterBottom color="error">
                  应用出现错误
                </Typography>
                
                <Typography variant="body1" color="text.secondary" paragraph>
                  很抱歉，KubeX平台遇到了一个意外错误。请尝试刷新页面或联系技术支持。
                </Typography>

                <Alert severity="error" sx={{ mb: 3, textAlign: 'left' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    错误详情:
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {this.state.error?.message}
                  </Typography>
                </Alert>

                <Box display="flex" gap={2} justifyContent="center">
                  <Button
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    onClick={this.handleReload}
                  >
                    刷新页面
                  </Button>
                  
                  <Button
                    variant="outlined"
                    onClick={this.handleReset}
                  >
                    重试
                  </Button>
                </Box>

                {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                  <Box mt={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      开发调试信息:
                    </Typography>
                    <pre style={{
                      background: '#f5f5f5',
                      padding: '16px',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '12px',
                      textAlign: 'left',
                      maxHeight: '200px'
                    }}>
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Box>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
