
// src/components/ConfigDialog.tsx

import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, Button, CircularProgress, Alert } from '@mui/material';
import type { RancherConfig } from '../services/rancherApi';

interface Props {
  open: boolean;
  onClose: () => void;
  onSave: (config: RancherConfig) => void;
  initialConfig?: RancherConfig | null;
}

const ConfigDialog: React.FC<Props> = ({ open, onClose, onSave, initialConfig }) => {
  const [baseUrl, setBaseUrl] = useState('');
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (initialConfig) {
      setBaseUrl(initialConfig.baseUrl);
      setToken(initialConfig.token);
    } else {
      // 加载已保存的配置
      const saved = localStorage.getItem('rancherConfig');
      if (saved) {
        const config = JSON.parse(saved);
        setBaseUrl(config.baseUrl || '');
        setToken(config.token || '');
      }
    }
  }, [initialConfig, open]);

  const handleSave = async () => {
    if (!baseUrl.trim() || !token.trim()) {
      setError('请填写完整的 Rancher 服务器地址和 Token');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const config = { baseUrl: baseUrl.trim(), token: token.trim() };
      onSave(config);
      onClose();
    } catch (error) {
      setError('保存配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setError(null);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
      <DialogTitle>配置 Rancher 连接</DialogTitle>
      <DialogContent>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        <TextField
          autoFocus
          margin="dense"
          label="Rancher 服务器地址"
          type="text"
          fullWidth
          variant="outlined"
          value={baseUrl}
          onChange={(e) => setBaseUrl(e.target.value)}
          placeholder="例如: https://rancher.mycompany.com"
          sx={{ mb: 2 }}
        />
        <TextField
          margin="dense"
          label="Bearer Token"
          type="password"
          fullWidth
          variant="outlined"
          value={token}
          onChange={(e) => setToken(e.target.value)}
          placeholder="token-xxxxx:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
          helperText="在 Rancher 界面中生成 API Token"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          取消
        </Button>
        <Button onClick={handleSave} variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={24} /> : '保存并连接'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfigDialog;
