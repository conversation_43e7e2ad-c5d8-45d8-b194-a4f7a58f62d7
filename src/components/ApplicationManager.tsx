import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typography,
  Button,
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Tooltip,
  Alert
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Delete,
  Restart,
  MoreVert,
  Scale,
  Info
} from '@mui/icons-material';
import type { Deployment } from '../services/rancherApi';

interface Props {
  deployments: Deployment[];
  onStart: (namespace: string, name: string, replicas?: number) => Promise<void>;
  onStop: (namespace: string, name: string) => Promise<void>;
  onDelete: (namespace: string, name: string) => Promise<void>;
  onRestart: (namespace: string, name: string) => Promise<void>;
  onScale: (namespace: string, name: string, replicas: number) => Promise<void>;
  loading?: boolean;
}

const ApplicationManager: React.FC<Props> = ({
  deployments,
  onStart,
  onStop,
  onDelete,
  onRestart,
  onScale,
  loading = false
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedApp, setSelectedApp] = useState<Deployment | null>(null);
  const [scaleDialogOpen, setScaleDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [scaleReplicas, setScaleReplicas] = useState(1);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, app: Deployment) => {
    setAnchorEl(event.currentTarget);
    setSelectedApp(app);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedApp(null);
  };

  const handleAction = async (action: () => Promise<void>, actionName: string) => {
    if (!selectedApp) return;
    
    setActionLoading(`${selectedApp.name}-${actionName}`);
    setError(null);
    
    try {
      await action();
    } catch (err) {
      setError(`${actionName}失败: ${err.message}`);
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const handleStart = () => {
    if (!selectedApp) return;
    handleAction(() => onStart(selectedApp.namespace, selectedApp.name), 'start');
  };

  const handleStop = () => {
    if (!selectedApp) return;
    handleAction(() => onStop(selectedApp.namespace, selectedApp.name), 'stop');
  };

  const handleRestart = () => {
    if (!selectedApp) return;
    handleAction(() => onRestart(selectedApp.namespace, selectedApp.name), 'restart');
  };

  const handleScaleOpen = () => {
    if (!selectedApp) return;
    setScaleReplicas(selectedApp.replicas);
    setScaleDialogOpen(true);
    handleMenuClose();
  };

  const handleScaleConfirm = async () => {
    if (!selectedApp) return;
    
    setActionLoading(`${selectedApp.name}-scale`);
    try {
      await onScale(selectedApp.namespace, selectedApp.name, scaleReplicas);
      setScaleDialogOpen(false);
    } catch (err) {
      setError(`扩缩容失败: ${err.message}`);
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteOpen = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    if (!selectedApp) return;
    
    setActionLoading(`${selectedApp.name}-delete`);
    try {
      await onDelete(selectedApp.namespace, selectedApp.name);
      setDeleteDialogOpen(false);
    } catch (err) {
      setError(`删除失败: ${err.message}`);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (deployment: Deployment) => {
    if (deployment.replicas === 0) return 'default';
    if (deployment.availableReplicas === deployment.replicas) return 'success';
    if (deployment.availableReplicas > 0) return 'warning';
    return 'error';
  };

  const getStatusText = (deployment: Deployment) => {
    if (deployment.replicas === 0) return '已停止';
    if (deployment.availableReplicas === deployment.replicas) return '运行中';
    if (deployment.availableReplicas > 0) return '部分运行';
    return '异常';
  };

  const isActionLoading = (appName: string, action: string) => {
    return actionLoading === `${appName}-${action}`;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        应用管理
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={2}>
        {deployments.map((deployment) => (
          <Grid item xs={12} sm={6} md={4} key={deployment.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Typography variant="h6" component="div" noWrap>
                    {deployment.name}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, deployment)}
                    disabled={loading}
                  >
                    <MoreVert />
                  </IconButton>
                </Box>

                <Typography color="text.secondary" gutterBottom>
                  {deployment.namespace}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Chip
                    label={getStatusText(deployment)}
                    color={getStatusColor(deployment)}
                    size="small"
                  />
                  <Chip
                    label={`${deployment.availableReplicas}/${deployment.replicas}`}
                    variant="outlined"
                    size="small"
                  />
                </Box>

                <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                  {deployment.image}
                </Typography>
              </CardContent>

              <CardActions>
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  {deployment.replicas === 0 ? (
                    <Tooltip title="启动应用">
                      <Button
                        size="small"
                        startIcon={<PlayArrow />}
                        onClick={() => onStart(deployment.namespace, deployment.name)}
                        disabled={loading || isActionLoading(deployment.name, 'start')}
                        color="success"
                      >
                        启动
                      </Button>
                    </Tooltip>
                  ) : (
                    <Tooltip title="停止应用">
                      <Button
                        size="small"
                        startIcon={<Stop />}
                        onClick={() => onStop(deployment.namespace, deployment.name)}
                        disabled={loading || isActionLoading(deployment.name, 'stop')}
                        color="warning"
                      >
                        停止
                      </Button>
                    </Tooltip>
                  )}

                  <Tooltip title="重启应用">
                    <Button
                      size="small"
                      startIcon={<Restart />}
                      onClick={() => onRestart(deployment.namespace, deployment.name)}
                      disabled={loading || isActionLoading(deployment.name, 'restart') || deployment.replicas === 0}
                    >
                      重启
                    </Button>
                  </Tooltip>
                </Box>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 右键菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleScaleOpen}>
          <Scale sx={{ mr: 1 }} />
          扩缩容
        </MenuItem>
        <MenuItem onClick={handleRestart} disabled={!selectedApp || selectedApp.replicas === 0}>
          <Restart sx={{ mr: 1 }} />
          重启
        </MenuItem>
        <MenuItem onClick={handleDeleteOpen} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>

      {/* 扩缩容对话框 */}
      <Dialog open={scaleDialogOpen} onClose={() => setScaleDialogOpen(false)}>
        <DialogTitle>扩缩容应用</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            应用: {selectedApp?.name}
          </Typography>
          <TextField
            autoFocus
            margin="dense"
            label="副本数"
            type="number"
            fullWidth
            variant="outlined"
            value={scaleReplicas}
            onChange={(e) => setScaleReplicas(parseInt(e.target.value) || 0)}
            inputProps={{ min: 0, max: 10 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setScaleDialogOpen(false)}>取消</Button>
          <Button onClick={handleScaleConfirm} variant="contained" disabled={actionLoading !== null}>
            确认
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>删除应用</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除应用 <strong>{selectedApp?.name}</strong> 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained" disabled={actionLoading !== null}>
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ApplicationManager;
