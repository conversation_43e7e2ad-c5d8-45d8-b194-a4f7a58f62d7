import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  LinearProgress,
  Card,
  CardContent,
  Fade,
  Backdrop
} from '@mui/material';
import {
  CloudQueue as CloudIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

interface LoadingScreenProps {
  message?: string;
  variant?: 'initial' | 'page' | 'overlay';
  progress?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = '正在加载...', 
  variant = 'initial',
  progress 
}) => {
  if (variant === 'overlay') {
    return (
      <Backdrop
        sx={{ 
          color: '#fff', 
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(4px)'
        }}
        open={true}
      >
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap={2}
        >
          <CircularProgress size={40} />
          <Typography variant="body1" color="inherit">
            {message}
          </Typography>
        </Box>
      </Backdrop>
    );
  }

  if (variant === 'page') {
    return (
      <Fade in={true}>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="200px"
          p={3}
        >
          <Card sx={{ minWidth: 300, textAlign: 'center' }}>
            <CardContent>
              <CircularProgress sx={{ mb: 2 }} />
              <Typography variant="body1" color="text.secondary">
                {message}
              </Typography>
              {progress !== undefined && (
                <Box mt={2}>
                  <LinearProgress 
                    variant="determinate" 
                    value={progress} 
                    sx={{ borderRadius: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                    {Math.round(progress)}%
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      </Fade>
    );
  }

  // Initial loading screen
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      bgcolor="background.default"
      p={3}
    >
      <Fade in={true}>
        <Card 
          elevation={8}
          sx={{ 
            p: 4, 
            textAlign: 'center',
            minWidth: 400,
            background: 'linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}
        >
          <CardContent>
            {/* Logo区域 */}
            <Box mb={3}>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                mb={2}
              >
                <CloudIcon sx={{ fontSize: 48, color: 'primary.main', mr: 1 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  KubeX
                </Typography>
              </Box>
              <Typography variant="subtitle1" color="text.secondary">
                Kubernetes 管理平台
              </Typography>
            </Box>

            {/* 加载动画 */}
            <Box mb={3}>
              <CircularProgress 
                size={50} 
                thickness={4}
                sx={{ 
                  color: 'primary.main',
                  '& .MuiCircularProgress-circle': {
                    strokeLinecap: 'round',
                  }
                }}
              />
            </Box>

            {/* 加载信息 */}
            <Typography variant="body1" color="text.primary" gutterBottom>
              {message}
            </Typography>

            {/* 进度条 */}
            {progress !== undefined && (
              <Box mt={2}>
                <LinearProgress 
                  variant="determinate" 
                  value={progress}
                  sx={{ 
                    borderRadius: 1,
                    height: 6,
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 1,
                    }
                  }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                  {Math.round(progress)}% 完成
                </Typography>
              </Box>
            )}

            {/* 系统信息 */}
            <Box mt={3} pt={2} borderTop="1px solid rgba(255, 255, 255, 0.1)">
              <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                <SettingsIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  正在初始化系统组件...
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Fade>
    </Box>
  );
};

export default LoadingScreen;
