import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Chip,
  Tooltip
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  NetworkCheck as NetworkIcon
} from '@mui/icons-material';

interface PerformanceMetrics {
  loadTime: number;
  memoryUsage: number;
  apiLatency: number;
  renderTime: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    memoryUsage: 0,
    apiLatency: 0,
    renderTime: 0
  });

  useEffect(() => {
    // 监控页面加载时间
    const loadTime = performance.now();
    
    // 监控内存使用情况
    const updateMetrics = () => {
      const memory = (performance as any).memory;
      const memoryUsage = memory ? Math.round(memory.usedJSHeapSize / 1024 / 1024) : 0;
      
      setMetrics(prev => ({
        ...prev,
        loadTime: Math.round(loadTime),
        memoryUsage,
        renderTime: Math.round(performance.now() - loadTime)
      }));
    };

    updateMetrics();
    
    // 每5秒更新一次指标
    const interval = setInterval(updateMetrics, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const getPerformanceColor = (value: number, thresholds: [number, number]) => {
    if (value < thresholds[0]) return 'success';
    if (value < thresholds[1]) return 'warning';
    return 'error';
  };

  if (process.env.NODE_ENV !== 'development') {
    return null; // 只在开发环境显示
  }

  return (
    <Box
      position="fixed"
      bottom={16}
      right={16}
      zIndex={9999}
      sx={{
        background: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(10px)',
        borderRadius: 2,
        p: 1,
        display: 'flex',
        gap: 1,
        flexWrap: 'wrap',
        maxWidth: 300
      }}
    >
      <Tooltip title="页面加载时间">
        <Chip
          icon={<SpeedIcon />}
          label={`${metrics.loadTime}ms`}
          size="small"
          color={getPerformanceColor(metrics.loadTime, [1000, 3000])}
          variant="outlined"
        />
      </Tooltip>
      
      <Tooltip title="内存使用量">
        <Chip
          icon={<MemoryIcon />}
          label={`${metrics.memoryUsage}MB`}
          size="small"
          color={getPerformanceColor(metrics.memoryUsage, [50, 100])}
          variant="outlined"
        />
      </Tooltip>
      
      <Tooltip title="渲染时间">
        <Chip
          icon={<NetworkIcon />}
          label={`${metrics.renderTime}ms`}
          size="small"
          color={getPerformanceColor(metrics.renderTime, [100, 500])}
          variant="outlined"
        />
      </Tooltip>
    </Box>
  );
};

export default PerformanceMonitor;
