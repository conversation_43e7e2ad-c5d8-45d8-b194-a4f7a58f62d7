import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  Accordion<PERSON><PERSON>ils,
  <PERSON><PERSON>,
  Divider
} from '@mui/material';
import {
  Add,
  Delete,
  ExpandMore,
  Deploy,
  Settings,
  Storage,
  NetworkCheck
} from '@mui/icons-material';

interface EnvironmentVariable {
  name: string;
  value: string;
}

interface Port {
  containerPort: number;
  protocol: string;
}

interface ResourceLimit {
  cpu: string;
  memory: string;
}

interface DeploymentConfig {
  name: string;
  image: string;
  namespace: string;
  replicas: number;
  env: EnvironmentVariable[];
  ports: Port[];
  resources: {
    requests: ResourceLimit;
    limits: ResourceLimit;
  };
  labels: Record<string, string>;
}

interface Props {
  namespaces: string[];
  onDeploy: (config: DeploymentConfig) => Promise<void>;
  loading?: boolean;
}

const ApplicationDeployment: React.FC<Props> = ({ namespaces, onDeploy, loading = false }) => {
  const [config, setConfig] = useState<DeploymentConfig>({
    name: '',
    image: '',
    namespace: '',
    replicas: 1,
    env: [],
    ports: [],
    resources: {
      requests: { cpu: '100m', memory: '128Mi' },
      limits: { cpu: '500m', memory: '512Mi' }
    },
    labels: {}
  });

  const [error, setError] = useState<string | null>(null);
  const [deploying, setDeploying] = useState(false);

  const commonImages = [
    'nginx:latest',
    'nginx:alpine',
    'httpd:latest',
    'redis:latest',
    'redis:alpine',
    'mysql:8.0',
    'postgres:15',
    'mongo:latest',
    'node:18-alpine',
    'python:3.11-alpine',
    'openjdk:17-alpine'
  ];

  const handleDeploy = async () => {
    setError(null);

    // 验证必填字段
    if (!config.name.trim()) {
      setError('应用名称不能为空');
      return;
    }
    if (!config.image.trim()) {
      setError('镜像地址不能为空');
      return;
    }
    if (!config.namespace) {
      setError('请选择命名空间');
      return;
    }

    // 验证应用名称格式
    if (!/^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/.test(config.name)) {
      setError('应用名称只能包含小写字母、数字和连字符，且必须以字母或数字开头和结尾');
      return;
    }

    setDeploying(true);
    try {
      await onDeploy(config);
      // 重置表单
      setConfig({
        name: '',
        image: '',
        namespace: '',
        replicas: 1,
        env: [],
        ports: [],
        resources: {
          requests: { cpu: '100m', memory: '128Mi' },
          limits: { cpu: '500m', memory: '512Mi' }
        },
        labels: {}
      });
    } catch (err) {
      setError(`部署失败: ${err.message}`);
    } finally {
      setDeploying(false);
    }
  };

  const addEnvironmentVariable = () => {
    setConfig(prev => ({
      ...prev,
      env: [...prev.env, { name: '', value: '' }]
    }));
  };

  const updateEnvironmentVariable = (index: number, field: 'name' | 'value', value: string) => {
    setConfig(prev => ({
      ...prev,
      env: prev.env.map((env, i) => i === index ? { ...env, [field]: value } : env)
    }));
  };

  const removeEnvironmentVariable = (index: number) => {
    setConfig(prev => ({
      ...prev,
      env: prev.env.filter((_, i) => i !== index)
    }));
  };

  const addPort = () => {
    setConfig(prev => ({
      ...prev,
      ports: [...prev.ports, { containerPort: 8080, protocol: 'TCP' }]
    }));
  };

  const updatePort = (index: number, field: 'containerPort' | 'protocol', value: string | number) => {
    setConfig(prev => ({
      ...prev,
      ports: prev.ports.map((port, i) => i === index ? { ...port, [field]: value } : port)
    }));
  };

  const removePort = (index: number) => {
    setConfig(prev => ({
      ...prev,
      ports: prev.ports.filter((_, i) => i !== index)
    }));
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Deploy />
          应用部署
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* 基本配置 */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Settings />
              基本配置
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="应用名称"
                  value={config.name}
                  onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value.toLowerCase() }))}
                  helperText="只能包含小写字母、数字和连字符"
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>命名空间</InputLabel>
                  <Select
                    value={config.namespace}
                    label="命名空间"
                    onChange={(e) => setConfig(prev => ({ ...prev, namespace: e.target.value }))}
                  >
                    {namespaces.map((ns) => (
                      <MenuItem key={ns} value={ns}>{ns}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="镜像地址"
                  value={config.image}
                  onChange={(e) => setConfig(prev => ({ ...prev, image: e.target.value }))}
                  placeholder="例如: nginx:latest"
                  required
                />
                <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {commonImages.map((image) => (
                    <Chip
                      key={image}
                      label={image}
                      size="small"
                      clickable
                      onClick={() => setConfig(prev => ({ ...prev, image }))}
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="副本数"
                  type="number"
                  value={config.replicas}
                  onChange={(e) => setConfig(prev => ({ ...prev, replicas: parseInt(e.target.value) || 1 }))}
                  inputProps={{ min: 1, max: 10 }}
                />
              </Grid>
            </Grid>
          </Grid>

          {/* 高级配置 */}
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <NetworkCheck />
                  网络配置
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle2">端口配置</Typography>
                  <Button startIcon={<Add />} onClick={addPort} size="small">
                    添加端口
                  </Button>
                </Box>
                {config.ports.map((port, index) => (
                  <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
                    <TextField
                      label="端口"
                      type="number"
                      value={port.containerPort}
                      onChange={(e) => updatePort(index, 'containerPort', parseInt(e.target.value) || 8080)}
                      size="small"
                      sx={{ width: 120 }}
                    />
                    <FormControl size="small" sx={{ width: 100 }}>
                      <InputLabel>协议</InputLabel>
                      <Select
                        value={port.protocol}
                        label="协议"
                        onChange={(e) => updatePort(index, 'protocol', e.target.value)}
                      >
                        <MenuItem value="TCP">TCP</MenuItem>
                        <MenuItem value="UDP">UDP</MenuItem>
                      </Select>
                    </FormControl>
                    <IconButton onClick={() => removePort(index)} size="small">
                      <Delete />
                    </IconButton>
                  </Box>
                ))}
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Settings />
                  环境变量
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle2">环境变量</Typography>
                  <Button startIcon={<Add />} onClick={addEnvironmentVariable} size="small">
                    添加变量
                  </Button>
                </Box>
                {config.env.map((env, index) => (
                  <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
                    <TextField
                      label="变量名"
                      value={env.name}
                      onChange={(e) => updateEnvironmentVariable(index, 'name', e.target.value)}
                      size="small"
                      sx={{ flex: 1 }}
                    />
                    <TextField
                      label="值"
                      value={env.value}
                      onChange={(e) => updateEnvironmentVariable(index, 'value', e.target.value)}
                      size="small"
                      sx={{ flex: 2 }}
                    />
                    <IconButton onClick={() => removeEnvironmentVariable(index)} size="small">
                      <Delete />
                    </IconButton>
                  </Box>
                ))}
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Storage />
                  资源限制
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>资源请求</Typography>
                    <TextField
                      fullWidth
                      label="CPU"
                      value={config.resources.requests.cpu}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        resources: { ...prev.resources, requests: { ...prev.resources.requests, cpu: e.target.value } }
                      }))}
                      size="small"
                      sx={{ mb: 1 }}
                      helperText="例如: 100m, 0.1"
                    />
                    <TextField
                      fullWidth
                      label="内存"
                      value={config.resources.requests.memory}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        resources: { ...prev.resources, requests: { ...prev.resources.requests, memory: e.target.value } }
                      }))}
                      size="small"
                      helperText="例如: 128Mi, 1Gi"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>资源限制</Typography>
                    <TextField
                      fullWidth
                      label="CPU"
                      value={config.resources.limits.cpu}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        resources: { ...prev.resources, limits: { ...prev.resources.limits, cpu: e.target.value } }
                      }))}
                      size="small"
                      sx={{ mb: 1 }}
                      helperText="例如: 500m, 1"
                    />
                    <TextField
                      fullWidth
                      label="内存"
                      value={config.resources.limits.memory}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        resources: { ...prev.resources, limits: { ...prev.resources.limits, memory: e.target.value } }
                      }))}
                      size="small"
                      helperText="例如: 512Mi, 2Gi"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>

          {/* 部署按钮 */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<Deploy />}
                onClick={handleDeploy}
                disabled={loading || deploying}
              >
                {deploying ? '部署中...' : '部署应用'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default ApplicationDeployment;
