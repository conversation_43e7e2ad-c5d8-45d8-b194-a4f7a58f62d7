import React, { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  Chip,
  IconButton,
  Typography,
  Alert
} from '@mui/material';
import { Add, Edit, Delete } from '@mui/icons-material';
import type { WorkspaceConfig, Namespace } from '../services/rancherApi';

interface Props {
  workspaces: WorkspaceConfig[];
  selectedWorkspace: string;
  onWorkspaceChange: (workspaceId: string) => void;
  onWorkspaceCreate: (workspace: Omit<WorkspaceConfig, 'id' | 'created'>) => void;
  onWorkspaceUpdate: (workspace: WorkspaceConfig) => void;
  onWorkspaceDelete: (workspaceId: string) => void;
  availableNamespaces: Namespace[];
  loading?: boolean;
}

const WorkspaceSelector: React.FC<Props> = ({
  workspaces,
  selectedWorkspace,
  onWorkspaceChange,
  onWorkspaceCreate,
  onWorkspaceUpdate,
  onWorkspaceDelete,
  availableNamespaces,
  loading = false
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingWorkspace, setEditingWorkspace] = useState<WorkspaceConfig | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    namespaces: [] as string[]
  });
  const [error, setError] = useState<string | null>(null);

  const handleCreateWorkspace = () => {
    setEditingWorkspace(null);
    setFormData({ name: '', description: '', namespaces: [] });
    setDialogOpen(true);
  };

  const handleEditWorkspace = (workspace: WorkspaceConfig) => {
    setEditingWorkspace(workspace);
    setFormData({
      name: workspace.name,
      description: workspace.description || '',
      namespaces: workspace.namespaces
    });
    setDialogOpen(true);
  };

  const handleSaveWorkspace = () => {
    if (!formData.name.trim()) {
      setError('工作区名称不能为空');
      return;
    }

    if (formData.namespaces.length === 0) {
      setError('请至少选择一个命名空间');
      return;
    }

    if (editingWorkspace) {
      onWorkspaceUpdate({
        ...editingWorkspace,
        name: formData.name,
        description: formData.description,
        namespaces: formData.namespaces
      });
    } else {
      onWorkspaceCreate({
        name: formData.name,
        description: formData.description,
        namespaces: formData.namespaces,
        labels: { 'app.kubernetes.io/managed-by': 'rancher-dashboard' }
      });
    }

    setDialogOpen(false);
    setError(null);
  };

  const handleDeleteWorkspace = (workspaceId: string) => {
    if (window.confirm('确定要删除这个工作区吗？这不会删除实际的命名空间。')) {
      onWorkspaceDelete(workspaceId);
    }
  };

  const handleNamespaceToggle = (namespace: string) => {
    setFormData(prev => ({
      ...prev,
      namespaces: prev.namespaces.includes(namespace)
        ? prev.namespaces.filter(ns => ns !== namespace)
        : [...prev.namespaces, namespace]
    }));
  };

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="workspace-select-label">工作区</InputLabel>
          <Select
            labelId="workspace-select-label"
            value={selectedWorkspace}
            label="工作区"
            onChange={(e) => onWorkspaceChange(e.target.value)}
            disabled={loading}
          >
            {workspaces.map((workspace) => (
              <MenuItem key={workspace.id} value={workspace.id}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                  <span>{workspace.name}</span>
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditWorkspace(workspace);
                      }}
                    >
                      <Edit fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteWorkspace(workspace.id);
                      }}
                    >
                      <Delete fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={handleCreateWorkspace}
          disabled={loading}
        >
          新建工作区
        </Button>

        {selectedWorkspace && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              命名空间:
            </Typography>
            {workspaces.find(w => w.id === selectedWorkspace)?.namespaces.map(ns => (
              <Chip key={ns} label={ns} size="small" />
            ))}
          </Box>
        )}
      </Box>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingWorkspace ? '编辑工作区' : '创建工作区'}
        </DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          
          <TextField
            autoFocus
            margin="dense"
            label="工作区名称"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2 }}
          />
          
          <TextField
            margin="dense"
            label="描述"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <Typography variant="subtitle2" gutterBottom>
            选择命名空间:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, maxHeight: 200, overflow: 'auto' }}>
            {availableNamespaces.map((namespace) => (
              <Chip
                key={namespace.name}
                label={namespace.name}
                clickable
                color={formData.namespaces.includes(namespace.name) ? 'primary' : 'default'}
                onClick={() => handleNamespaceToggle(namespace.name)}
                variant={formData.namespaces.includes(namespace.name) ? 'filled' : 'outlined'}
              />
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>取消</Button>
          <Button onClick={handleSaveWorkspace} variant="contained">
            {editingWorkspace ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default WorkspaceSelector;
