import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Grid,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import { Refresh, Warning, CheckCircle, Error, Schedule } from '@mui/icons-material';
import type { ResourceMetrics, Pod } from '../services/rancherApi';

interface Props {
  metrics: ResourceMetrics;
  pods: Pod[];
  onRefresh: () => void;
  loading?: boolean;
}

const ResourceMonitor: React.FC<Props> = ({ metrics, pods, onRefresh, loading = false }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running': return 'success';
      case 'pending': return 'warning';
      case 'failed': case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running': return <CheckCircle fontSize="small" />;
      case 'pending': return <Schedule fontSize="small" />;
      case 'failed': case 'error': return <Error fontSize="small" />;
      default: return <Warning fontSize="small" />;
    }
  };

  const formatMemory = (memory: string) => {
    if (memory.endsWith('Ki')) {
      return `${Math.round(parseInt(memory.slice(0, -2)) / 1024)}Mi`;
    }
    return memory;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">资源监控</Typography>
        <Tooltip title="刷新数据">
          <IconButton onClick={onRefresh} disabled={loading}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={3}>
        {/* CPU 使用率 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                CPU 使用率
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" color="primary">
                  {metrics.cpu.percentage}%
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={metrics.cpu.percentage} 
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                {metrics.cpu.used} / {metrics.cpu.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* 内存使用率 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                内存使用率
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" color="primary">
                  {metrics.memory.percentage}%
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={metrics.memory.percentage} 
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                {formatMemory(metrics.memory.used)} / {formatMemory(metrics.memory.total)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Pod 状态统计 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Pod 状态
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">运行中</Typography>
                  <Chip 
                    label={metrics.pods.running} 
                    color="success" 
                    size="small"
                    icon={<CheckCircle fontSize="small" />}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">等待中</Typography>
                  <Chip 
                    label={metrics.pods.pending} 
                    color="warning" 
                    size="small"
                    icon={<Schedule fontSize="small" />}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">失败</Typography>
                  <Chip 
                    label={metrics.pods.failed} 
                    color="error" 
                    size="small"
                    icon={<Error fontSize="small" />}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pt: 1, borderTop: 1, borderColor: 'divider' }}>
                  <Typography variant="body2" fontWeight="bold">总计</Typography>
                  <Typography variant="body2" fontWeight="bold">{metrics.pods.total}</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Pod 详细列表 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Pod 详情
              </Typography>
              {pods.length === 0 ? (
                <Typography color="text.secondary">没有找到 Pod</Typography>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {pods.slice(0, 10).map((pod) => (
                    <Box
                      key={pod.id}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        p: 1,
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        '&:hover': { bgcolor: 'action.hover' }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getStatusIcon(pod.phase)}
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {pod.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {pod.namespace} • {pod.nodeName || 'N/A'}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={pod.phase}
                          color={getStatusColor(pod.phase)}
                          size="small"
                        />
                        <Typography variant="caption" color="text.secondary">
                          {pod.containers.length} 容器
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                  {pods.length > 10 && (
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', pt: 1 }}>
                      还有 {pods.length - 10} 个 Pod...
                    </Typography>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ResourceMonitor;
