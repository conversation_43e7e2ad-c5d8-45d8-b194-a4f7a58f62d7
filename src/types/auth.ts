// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',
  OPERATOR = 'operator'
}

// 权限枚举
export enum Permission {
  // 工作区权限
  VIEW_ALL_WORKSPACES = 'view_all_workspaces',
  CREATE_WORKSPACE = 'create_workspace',
  EDIT_WORKSPACE = 'edit_workspace',
  DELETE_WORKSPACE = 'delete_workspace',
  
  // 用户管理权限
  MANAGE_USERS = 'manage_users',
  VIEW_USERS = 'view_users',
  
  // 应用管理权限
  DEPLOY_APP = 'deploy_app',
  MANAGE_APP = 'manage_app',
  VIEW_APP = 'view_app',
  
  // 监控权限
  VIEW_MONITORING = 'view_monitoring',
  
  // 系统权限
  VIEW_AUDIT_LOGS = 'view_audit_logs',
  SYSTEM_CONFIG = 'system_config'
}

// 用户接口
export interface User {
  id: string;
  username: string;
  email?: string;
  role: UserRole;
  workspaceIds: string[]; // 分配给用户的工作区ID列表
  permissions: Permission[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

// 登录凭据
export interface LoginCredentials {
  username: string;
  password: string;
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
}

// 操作日志
export interface AuditLog {
  id: string;
  userId: string;
  username: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  timestamp: string;
  ip?: string;
  userAgent?: string;
}

// 角色权限映射
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    Permission.VIEW_ALL_WORKSPACES,
    Permission.CREATE_WORKSPACE,
    Permission.EDIT_WORKSPACE,
    Permission.DELETE_WORKSPACE,
    Permission.MANAGE_USERS,
    Permission.VIEW_USERS,
    Permission.DEPLOY_APP,
    Permission.MANAGE_APP,
    Permission.VIEW_APP,
    Permission.VIEW_MONITORING,
    Permission.VIEW_AUDIT_LOGS,
    Permission.SYSTEM_CONFIG
  ],
  [UserRole.OPERATOR]: [
    Permission.DEPLOY_APP,
    Permission.MANAGE_APP,
    Permission.VIEW_APP,
    Permission.VIEW_MONITORING
  ]
};

// 默认用户（用于开发和演示）
export const DEFAULT_USERS: User[] = [
  {
    id: 'admin-001',
    username: 'admin',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    workspaceIds: [], // 管理员可以访问所有工作区
    permissions: ROLE_PERMISSIONS[UserRole.ADMIN],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'operator-001',
    username: 'operator1',
    email: '<EMAIL>',
    role: UserRole.OPERATOR,
    workspaceIds: [], // 将在创建工作区时分配
    permissions: ROLE_PERMISSIONS[UserRole.OPERATOR],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'operator-002',
    username: 'operator2',
    email: '<EMAIL>',
    role: UserRole.OPERATOR,
    workspaceIds: [],
    permissions: ROLE_PERMISSIONS[UserRole.OPERATOR],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// 权限检查函数
export const hasPermission = (user: User | null, permission: Permission): boolean => {
  if (!user || !user.isActive) return false;
  return user.permissions.includes(permission);
};

// 检查用户是否可以访问工作区
export const canAccessWorkspace = (user: User | null, workspaceId: string): boolean => {
  if (!user || !user.isActive) return false;
  
  // 管理员可以访问所有工作区
  if (user.role === UserRole.ADMIN) return true;
  
  // 操作人员只能访问分配给自己的工作区
  return user.workspaceIds.includes(workspaceId);
};

// 获取用户可访问的工作区ID列表
export const getAccessibleWorkspaceIds = (user: User | null, allWorkspaceIds: string[]): string[] => {
  if (!user || !user.isActive) return [];
  
  // 管理员可以访问所有工作区
  if (user.role === UserRole.ADMIN) return allWorkspaceIds;
  
  // 操作人员只能访问分配给自己的工作区
  return user.workspaceIds.filter(id => allWorkspaceIds.includes(id));
};
