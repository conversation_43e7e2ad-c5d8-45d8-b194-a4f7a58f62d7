
// src/services/rancherApi.ts

export interface RancherConfig {
  baseUrl: string;
  token: string;
  clusterId?: string;
}

export interface Deployment {
  id: string;
  name: string;
  namespace: string;
  replicas: number;
  availableReplicas: number;
  image: string;
  state: string;
  created?: string;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
}

export interface Pod {
  id: string;
  name: string;
  namespace: string;
  state: string;
  phase: string;
  nodeName?: string;
  podIP?: string;
  created: string;
  containers: Container[];
  labels?: Record<string, string>;
}

export interface Container {
  name: string;
  image: string;
  state: string;
  ready: boolean;
  restartCount: number;
}

export interface Service {
  id: string;
  name: string;
  namespace: string;
  type: string;
  clusterIP: string;
  ports: ServicePort[];
  selector?: Record<string, string>;
  created: string;
}

export interface ServicePort {
  name?: string;
  port: number;
  targetPort: number | string;
  protocol: string;
}

export interface Namespace {
  id: string;
  name: string;
  state: string;
  created: string;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
}

export interface WorkspaceConfig {
  id: string;
  name: string;
  description?: string;
  namespaces: string[];
  created: string;
  labels?: Record<string, string>;
}

export interface ResourceMetrics {
  cpu: {
    used: string;
    total: string;
    percentage: number;
  };
  memory: {
    used: string;
    total: string;
    percentage: number;
  };
  pods: {
    running: number;
    pending: number;
    failed: number;
    total: number;
  };
}

export class RancherAPI {
  private config: RancherConfig;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private readonly CACHE_TTL = 30000; // 30 seconds default cache
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second

  constructor(config: RancherConfig) {
    this.config = {
      ...config,
      baseUrl: config.baseUrl.replace(/\/+$/, ''),
    };
  }

  private getHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.config.token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  private buildUrl(path: string): string {
    // 在开发环境中使用代理路径
    if (import.meta.env.DEV) {
      return `/rancher-api${path}`;
    }
    // 在生产环境中使用完整 URL
    return `${this.config.baseUrl}${path}`;
  }

  private getCacheKey(url: string, options?: any): string {
    return `${url}_${JSON.stringify(options || {})}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      console.log(`Cache hit for: ${key}`);
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  private setCache<T>(key: string, data: T, ttl: number = this.CACHE_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async fetchWithRetry(url: string, options: RequestInit = {}, retries: number = this.MAX_RETRIES): Promise<Response> {
    let lastError: Error;

    for (let i = 0; i <= retries; i++) {
      try {
        console.log(`Attempt ${i + 1}/${retries + 1} for: ${url}`);

        const response = await fetch(url, {
          ...options,
          headers: {
            ...this.getHeaders(),
            ...options.headers,
          },
          mode: 'cors',
        });

        if (response.ok) {
          return response;
        }

        // 如果是认证错误，不重试
        if (response.status === 401 || response.status === 403) {
          throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
        }

        // 如果是客户端错误（4xx），不重试
        if (response.status >= 400 && response.status < 500) {
          const errorText = await response.text();
          throw new Error(`Client error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        // 服务器错误（5xx）或网络错误，可以重试
        lastError = new Error(`Server error: ${response.status} ${response.statusText}`);

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${i + 1} failed:`, error.message);
      }

      // 如果不是最后一次尝试，等待后重试
      if (i < retries) {
        const delay = this.RETRY_DELAY * Math.pow(2, i); // 指数退避
        console.log(`Waiting ${delay}ms before retry...`);
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  // 获取服务列表
  async getServices(clusterId: string, namespace?: string): Promise<any[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = namespace
      ? this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces/${namespace}/services`)
      : this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/services`);

    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<any[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const services = data.items.map((item: any) => ({
        id: item.metadata?.uid || `service-${Date.now()}`,
        name: item.metadata?.name || 'unknown',
        namespace: item.metadata?.namespace || 'default',
        type: item.spec?.type || 'ClusterIP',
        clusterIP: item.spec?.clusterIP,
        ports: item.spec?.ports?.map((port: any) => `${port.port}/${port.protocol}`).join(', ') || 'None',
        selector: item.spec?.selector || {},
        created: item.metadata?.creationTimestamp,
        labels: item.metadata?.labels || {},
        annotations: item.metadata?.annotations || {},
      }));

      // 缓存结果
      this.setCache(cacheKey, services, 30000); // 缓存30秒

      console.log(`Loaded ${services.length} services from cluster ${actualClusterId}`);
      return services;
    } catch (error) {
      console.error('Failed to fetch services:', error.message);
      throw new Error(`Failed to fetch services: ${error.message}`);
    }
  }

  // 获取 Pod 列表
  async getPods(clusterId: string, namespace?: string): Promise<any[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = namespace
      ? this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces/${namespace}/pods`)
      : this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/pods`);

    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<any[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const pods = data.items.map((item: any) => {
        // 调试：打印原始 Pod 数据
        console.log('Raw pod item:', {
          name: item.metadata?.name,
          namespace: item.metadata?.namespace,
          phase: item.status?.phase,
          conditions: item.status?.conditions,
          containerStatuses: item.status?.containerStatuses
        });

        return {
          id: item.metadata?.uid || `pod-${Date.now()}`,
          name: item.metadata?.name || 'unknown',
          namespace: item.metadata?.namespace || 'default',
          status: item.status?.phase || 'Unknown',
          node: item.spec?.nodeName || 'unknown',
          podIP: item.status?.podIP,
          hostIP: item.status?.hostIP,
          containers: item.spec?.containers?.map((container: any) => ({
            name: container.name,
            image: container.image,
            ready: item.status?.containerStatuses?.find((cs: any) => cs.name === container.name)?.ready || false
          })) || [],
          created: item.metadata?.creationTimestamp,
          labels: item.metadata?.labels || {},
          annotations: item.metadata?.annotations || {},
        };
      });

      // 缓存结果
      this.setCache(cacheKey, pods, 15000); // 缓存15秒

      console.log(`Loaded ${pods.length} pods from cluster ${actualClusterId}`);
      return pods;
    } catch (error) {
      console.error('Failed to fetch pods:', error.message);
      throw new Error(`Failed to fetch pods: ${error.message}`);
    }
  }

  // 缓存管理方法
  public clearCache(): void {
    this.cache.clear();
    console.log('Rancher API cache cleared');
  }

  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // 连接状态监控
  public async getConnectionStatus(): Promise<{
    connected: boolean;
    latency?: number;
    error?: string;
  }> {
    const startTime = Date.now();
    try {
      const connected = await this.testConnection();
      const latency = Date.now() - startTime;
      return { connected, latency };
    } catch (error) {
      return {
        connected: false,
        error: error.message,
        latency: Date.now() - startTime
      };
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      console.log('Testing connection to Rancher API...');
      const url = this.buildUrl('/v3/clusters');

      const response = await this.fetchWithRetry(url);

      console.log('Connection test successful');
      return true;
    } catch (error) {
      console.error('Connection test failed:', error.message);
      return false;
    }
  }

  async getClusters(): Promise<any[]> {
    const url = this.buildUrl('/v3/clusters');
    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<any[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      // 缓存结果
      this.setCache(cacheKey, data.data, 60000); // 缓存1分钟

      console.log(`Loaded ${data.data.length} clusters from Rancher`);
      return data.data;
    } catch (error) {
      console.error('Failed to fetch clusters:', error.message);
      throw new Error(`Failed to fetch clusters: ${error.message}`);
    }
  }

  async getDeployments(clusterId: string, namespace?: string): Promise<Deployment[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = namespace
      ? this.buildUrl(`/k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments`)
      : this.buildUrl(`/k8s/clusters/${actualClusterId}/apis/apps/v1/deployments`);

    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<Deployment[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const deployments = data.items.map((item: any): Deployment => {
        // 调试：打印原始数据
        console.log('Raw deployment item:', {
          name: item.metadata?.name,
          namespace: item.metadata?.namespace,
          specReplicas: item.spec?.replicas,
          statusReplicas: item.status?.replicas,
          availableReplicas: item.status?.availableReplicas,
          readyReplicas: item.status?.readyReplicas,
          updatedReplicas: item.status?.updatedReplicas,
          status: item.status
        });

        // 安全地获取容器信息
        const containers = item.spec?.template?.spec?.containers || [];
        const firstContainer = containers[0] || {};

        // 安全地获取状态信息
        const conditions = item.status?.conditions || [];
        const availableCondition = conditions.find((c: any) => c.type === 'Available');
        const progressingCondition = conditions.find((c: any) => c.type === 'Progressing');

        // 更准确的状态判断
        let state = 'Inactive';
        if (availableCondition?.status === 'True') {
          state = 'Active';
        } else if (progressingCondition?.status === 'True') {
          state = 'Progressing';
        } else if (item.status?.replicas === 0) {
          state = 'Stopped';
        }

        const deployment = {
          id: item.metadata?.uid || `deployment-${Date.now()}`,
          name: item.metadata?.name || 'unknown',
          namespace: item.metadata?.namespace || 'default',
          replicas: item.spec?.replicas || 0,
          availableReplicas: item.status?.availableReplicas || 0,
          readyReplicas: item.status?.readyReplicas || 0,
          image: firstContainer.image || 'unknown',
          state: state,
          created: item.metadata?.creationTimestamp,
          labels: item.metadata?.labels || {},
          annotations: item.metadata?.annotations || {},
        };

        console.log('Processed deployment:', deployment);
        return deployment;
      });

      // 缓存结果
      this.setCache(cacheKey, deployments, 15000); // 缓存15秒

      console.log(`Loaded ${deployments.length} deployments from cluster ${actualClusterId}`);
      return deployments;
    } catch (error) {
      console.error('Failed to fetch deployments:', error.message);
      throw new Error(`Failed to fetch deployments: ${error.message}`);
    }
  }





  // Namespace management
  async getNamespaces(clusterId: string): Promise<Namespace[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces`);
    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<Namespace[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const namespaces = data.items.map((item: any) => ({
        id: item.metadata?.name || `ns-${Date.now()}`,
        name: item.metadata?.name || 'unknown',
        state: item.status?.phase || 'Unknown',
        created: item.metadata?.creationTimestamp || new Date().toISOString(),
        labels: item.metadata?.labels || {},
        annotations: item.metadata?.annotations || {},
      }));

      // 缓存结果
      this.setCache(cacheKey, namespaces, 60000); // 缓存1分钟

      console.log(`Loaded ${namespaces.length} namespaces from cluster ${actualClusterId}`);
      return namespaces;
    } catch (error) {
      console.error('Failed to fetch namespaces:', error.message);
      throw new Error(`Failed to fetch namespaces: ${error.message}`);
    }
  }

  async createNamespace(clusterId: string, name: string, labels?: Record<string, string>): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces`);

    const namespace = {
      apiVersion: 'v1',
      kind: 'Namespace',
      metadata: {
        name,
        labels: labels || {},
      },
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(namespace),
    });

    return response.ok;
  }

  async deleteNamespace(clusterId: string, name: string): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces/${name}`);

    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });

    return response.ok;
  }



  async deletePod(clusterId: string, namespace: string, name: string): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces/${namespace}/pods/${name}`);

    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });

    return response.ok;
  }







  // Application deployment
  async deployApplication(clusterId: string, namespace: string, appConfig: {
    name: string;
    image: string;
    replicas?: number;
    ports?: Array<{ containerPort: number; protocol?: string }>;
    env?: Array<{ name: string; value: string }>;
    resources?: {
      requests?: { cpu?: string; memory?: string };
      limits?: { cpu?: string; memory?: string };
    };
    labels?: Record<string, string>;
  }): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments`);

    const deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appConfig.name,
        namespace,
        labels: {
          app: appConfig.name,
          ...appConfig.labels,
        },
      },
      spec: {
        replicas: appConfig.replicas || 1,
        selector: {
          matchLabels: {
            app: appConfig.name,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appConfig.name,
              ...appConfig.labels,
            },
          },
          spec: {
            containers: [
              {
                name: appConfig.name,
                image: appConfig.image,
                ports: appConfig.ports || [],
                env: appConfig.env || [],
                resources: appConfig.resources || {},
              },
            ],
          },
        },
      },
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(deployment),
    });

    return response.ok;
  }

  // Resource metrics (basic implementation)
  async getResourceMetrics(clusterId: string, namespace?: string): Promise<ResourceMetrics> {
    try {
      const pods = await this.getPods(clusterId, namespace);

      const metrics: ResourceMetrics = {
        cpu: { used: '0m', total: '1000m', percentage: 0 },
        memory: { used: '0Mi', total: '1000Mi', percentage: 0 },
        pods: {
          running: pods.filter(p => p.phase === 'Running').length,
          pending: pods.filter(p => p.phase === 'Pending').length,
          failed: pods.filter(p => p.phase === 'Failed').length,
          total: pods.length,
        },
      };

      return metrics;
    } catch (error) {
      console.error('Failed to get resource metrics:', error);
      return {
        cpu: { used: '0m', total: '0m', percentage: 0 },
        memory: { used: '0Mi', total: '0Mi', percentage: 0 },
        pods: { running: 0, pending: 0, failed: 0, total: 0 },
      };
    }
  }



  async restartDeployment(clusterId: string, namespace: string, deploymentName: string): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments/${deploymentName}`);

    try {
      // 获取当前部署
      const getResponse = await this.fetchWithRetry(url);
      const deployment = await getResponse.json();

      // 添加重启注解
      if (!deployment.spec.template.metadata.annotations) {
        deployment.spec.template.metadata.annotations = {};
      }
      deployment.spec.template.metadata.annotations['kubectl.kubernetes.io/restartedAt'] = new Date().toISOString();

      // 更新部署
      const response = await this.fetchWithRetry(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deployment)
      });

      if (response.ok) {
        console.log(`Deployment ${deploymentName} restarted successfully`);
        this.clearCache();
        return true;
      } else {
        throw new Error(`Restart failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to restart deployment:', error.message);
      return false;
    }
  }

  async stopDeployment(clusterId: string, namespace: string, deploymentName: string): Promise<boolean> {
    console.log(`Stopping deployment: ${deploymentName} in ${namespace} on cluster ${clusterId}`);
    const result = await this.scaleDeployment(clusterId, namespace, deploymentName, 0);
    console.log(`Stop deployment result: ${result}`);
    return result;
  }

  async startDeployment(clusterId: string, namespace: string, deploymentName: string): Promise<boolean> {
    console.log(`Starting deployment: ${deploymentName} in ${namespace} on cluster ${clusterId}`);
    const result = await this.scaleDeployment(clusterId, namespace, deploymentName, 1);
    console.log(`Start deployment result: ${result}`);
    return result;
  }

  private async scaleDeployment(clusterId: string, namespace: string, deploymentName: string, replicas: number): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;

    console.log(`=== Scale Deployment ===`);
    console.log(`Deployment: ${deploymentName}`);
    console.log(`Namespace: ${namespace}`);
    console.log(`Target replicas: ${replicas}`);

    try {
      // 方法1: 尝试使用 scale 子资源
      const scaleUrl = this.buildUrl(`/k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments/${deploymentName}/scale`);
      console.log(`Scale URL: ${scaleUrl}`);

      const scaleManifest = {
        apiVersion: 'autoscaling/v1',
        kind: 'Scale',
        metadata: {
          name: deploymentName,
          namespace: namespace
        },
        spec: {
          replicas: replicas
        }
      };

      console.log('Scale manifest:', JSON.stringify(scaleManifest, null, 2));

      const scaleResponse = await this.fetchWithRetry(scaleUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scaleManifest)
      });

      console.log(`Scale response status: ${scaleResponse.status}`);

      if (scaleResponse.ok) {
        const responseData = await scaleResponse.json();
        console.log('Scale response data:', responseData);
        console.log(`✅ Deployment ${deploymentName} scaled to ${replicas} replicas using scale subresource`);
        this.clearCache();
        return true;
      } else {
        const errorText = await scaleResponse.text();
        console.warn(`⚠️ Scale subresource failed: ${scaleResponse.status} - ${errorText}`);
        console.log('Trying alternative method: patch deployment directly');

        // 方法2: 直接修改 deployment 的 replicas
        return await this.scaleDeploymentDirect(actualClusterId, namespace, deploymentName, replicas);
      }
    } catch (error) {
      console.warn('❌ Scale subresource failed:', error);
      console.log('Trying alternative method: patch deployment directly');

      // 方法2: 直接修改 deployment 的 replicas
      return await this.scaleDeploymentDirect(actualClusterId, namespace, deploymentName, replicas);
    }
  }

  private async scaleDeploymentDirect(clusterId: string, namespace: string, deploymentName: string, replicas: number): Promise<boolean> {
    const url = this.buildUrl(`/k8s/clusters/${clusterId}/apis/apps/v1/namespaces/${namespace}/deployments/${deploymentName}`);

    console.log(`=== Direct Scale Deployment ===`);
    console.log(`URL: ${url}`);

    try {
      // 首先获取当前的 deployment
      const getResponse = await this.fetchWithRetry(url);
      if (!getResponse.ok) {
        throw new Error(`Failed to get deployment: ${getResponse.status}`);
      }

      const deployment = await getResponse.json();
      console.log('Current deployment replicas:', deployment.spec?.replicas);

      // 修改 replicas
      deployment.spec.replicas = replicas;

      // 更新 deployment
      const putResponse = await this.fetchWithRetry(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deployment)
      });

      console.log(`Direct scale response status: ${putResponse.status}`);

      if (putResponse.ok) {
        const responseData = await putResponse.json();
        console.log('Direct scale response data:', responseData.spec?.replicas);
        console.log(`✅ Deployment ${deploymentName} scaled to ${replicas} replicas using direct method`);
        this.clearCache();
        return true;
      } else {
        const errorText = await putResponse.text();
        console.error(`❌ Direct scale failed: ${putResponse.status} - ${errorText}`);
        throw new Error(`Direct scale failed: ${putResponse.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('❌ Failed to scale deployment directly:', error);
      return false;
    }
  }

  async deleteDeployment(clusterId: string, namespace: string, deploymentName: string): Promise<boolean> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments/${deploymentName}`);

    try {
      const response = await this.fetchWithRetry(url, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log(`Deployment ${deploymentName} deleted successfully`);
        this.clearCache();
        return true;
      } else {
        throw new Error(`Delete failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to delete deployment:', error.message);
      return false;
    }
  }

  // 获取 ConfigMaps
  async getConfigMaps(clusterId: string, namespace?: string): Promise<any[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = namespace
      ? this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces/${namespace}/configmaps`)
      : this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/configmaps`);

    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<any[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const configMaps = data.items.map((item: any) => ({
        id: item.metadata?.uid || `configmap-${Date.now()}`,
        name: item.metadata?.name || 'unknown',
        namespace: item.metadata?.namespace || 'default',
        data: item.data || {},
        dataCount: Object.keys(item.data || {}).length,
        created: item.metadata?.creationTimestamp,
        labels: item.metadata?.labels || {},
        annotations: item.metadata?.annotations || {},
      }));

      // 缓存结果
      this.setCache(cacheKey, configMaps, 60000); // 缓存1分钟

      console.log(`Loaded ${configMaps.length} configmaps from cluster ${actualClusterId}`);
      return configMaps;
    } catch (error) {
      console.error('Failed to fetch configmaps:', error.message);
      throw new Error(`Failed to fetch configmaps: ${error.message}`);
    }
  }

  // 获取 Secrets
  async getSecrets(clusterId: string, namespace?: string): Promise<any[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = namespace
      ? this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/namespaces/${namespace}/secrets`)
      : this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/secrets`);

    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<any[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const secrets = data.items.map((item: any) => ({
        id: item.metadata?.uid || `secret-${Date.now()}`,
        name: item.metadata?.name || 'unknown',
        namespace: item.metadata?.namespace || 'default',
        type: item.type || 'Opaque',
        dataCount: Object.keys(item.data || {}).length,
        created: item.metadata?.creationTimestamp,
        labels: item.metadata?.labels || {},
        annotations: item.metadata?.annotations || {},
      }));

      // 缓存结果
      this.setCache(cacheKey, secrets, 60000); // 缓存1分钟

      console.log(`Loaded ${secrets.length} secrets from cluster ${actualClusterId}`);
      return secrets;
    } catch (error) {
      console.error('Failed to fetch secrets:', error.message);
      throw new Error(`Failed to fetch secrets: ${error.message}`);
    }
  }

  // 获取节点信息
  async getNodes(clusterId: string): Promise<any[]> {
    const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
    const url = this.buildUrl(`/k8s/clusters/${actualClusterId}/api/v1/nodes`);

    const cacheKey = this.getCacheKey(url);

    // 检查缓存
    const cached = this.getFromCache<any[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.fetchWithRetry(url);
      const data = await response.json();

      const nodes = data.items.map((item: any) => ({
        id: item.metadata?.uid || `node-${Date.now()}`,
        name: item.metadata?.name || 'unknown',
        status: this.getNodeStatus(item),
        ready: this.isNodeReady(item),
        roles: this.getNodeRoles(item),
        version: item.status?.nodeInfo?.kubeletVersion || 'unknown',
        os: item.status?.nodeInfo?.osImage || 'unknown',
        capacity: {
          cpu: item.status?.capacity?.cpu || '0',
          memory: item.status?.capacity?.memory || '0Ki',
          storage: item.status?.capacity?.['ephemeral-storage'] || '0Ki'
        },
        allocatable: {
          cpu: item.status?.allocatable?.cpu || '0',
          memory: item.status?.allocatable?.memory || '0Ki',
          storage: item.status?.allocatable?.['ephemeral-storage'] || '0Ki'
        },
        created: item.metadata?.creationTimestamp,
        labels: item.metadata?.labels || {},
        annotations: item.metadata?.annotations || {},
      }));

      // 缓存结果
      this.setCache(cacheKey, nodes, 30000); // 缓存30秒

      console.log(`Loaded ${nodes.length} nodes from cluster ${actualClusterId}`);
      return nodes;
    } catch (error) {
      console.error('Failed to fetch nodes:', error.message);
      throw new Error(`Failed to fetch nodes: ${error.message}`);
    }
  }

  // 获取节点状态
  private getNodeStatus(node: any): string {
    const conditions = node.status?.conditions || [];
    const readyCondition = conditions.find((c: any) => c.type === 'Ready');

    if (readyCondition?.status === 'True') {
      return 'Ready';
    } else if (readyCondition?.status === 'False') {
      return 'NotReady';
    } else {
      return 'Unknown';
    }
  }

  // 检查节点是否就绪
  private isNodeReady(node: any): boolean {
    const conditions = node.status?.conditions || [];
    const readyCondition = conditions.find((c: any) => c.type === 'Ready');
    return readyCondition?.status === 'True';
  }

  // 获取节点角色
  private getNodeRoles(node: any): string[] {
    const labels = node.metadata?.labels || {};
    const roles: string[] = [];

    if (labels['node-role.kubernetes.io/master'] !== undefined ||
        labels['node-role.kubernetes.io/control-plane'] !== undefined) {
      roles.push('master');
    }

    if (labels['node-role.kubernetes.io/worker'] !== undefined) {
      roles.push('worker');
    }

    return roles.length > 0 ? roles : ['worker'];
  }

  // 获取集群资源使用情况
  async getClusterMetrics(clusterId: string): Promise<any> {
    try {
      const nodes = await this.getNodes(clusterId);

      // 计算总容量和可分配资源
      let totalCpu = 0;
      let totalMemory = 0; // 以 GB 为单位
      let totalStorage = 0; // 以 GB 为单位

      nodes.forEach(node => {
        // CPU 处理 (通常是核心数)
        totalCpu += parseFloat(node.capacity.cpu) || 0;

        // 内存处理 (从 Ki 转换为 GB)
        const memoryKi = parseInt(node.capacity.memory.replace('Ki', '')) || 0;
        totalMemory += memoryKi / (1024 * 1024); // Ki -> GB

        // 存储处理 (从 Ki 转换为 GB)
        const storageKi = parseInt(node.capacity.storage.replace('Ki', '')) || 0;
        totalStorage += storageKi / (1024 * 1024); // Ki -> GB
      });

      // 模拟使用情况 (实际应该从 metrics-server 获取)
      const cpuUsed = totalCpu * 0.3; // 假设使用 30%
      const memoryUsed = totalMemory * 0.4; // 假设使用 40%
      const storageUsed = totalStorage * 0.36; // 假设使用 36%

      return {
        cpu: {
          total: Math.round(totalCpu * 10) / 10,
          used: Math.round(cpuUsed * 10) / 10,
          percentage: Math.round((cpuUsed / totalCpu) * 100)
        },
        memory: {
          total: Math.round(totalMemory * 10) / 10,
          used: Math.round(memoryUsed * 10) / 10,
          percentage: Math.round((memoryUsed / totalMemory) * 100)
        },
        storage: {
          total: Math.round(totalStorage),
          used: Math.round(storageUsed),
          percentage: Math.round((storageUsed / totalStorage) * 100)
        }
      };
    } catch (error) {
      console.error('Failed to get cluster metrics:', error);
      // 返回默认值
      return {
        cpu: { total: 8, used: 2.4, percentage: 30 },
        memory: { total: 32, used: 12.8, percentage: 40 },
        storage: { total: 500, used: 180, percentage: 36 }
      };
    }
  }
}
