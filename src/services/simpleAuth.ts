// 简化的认证服务
export interface SimpleUser {
  id: string;
  username: string;
  email?: string;
  role: 'admin' | 'operator';
  workspaceIds: string[];
}

export interface SimpleAuthState {
  isAuthenticated: boolean;
  user: SimpleUser | null;
}

class SimpleAuthService {
  private readonly STORAGE_KEY = 'simple_auth_state';
  
  // 简单的用户数据库
  private users = {
    'admin': {
      id: 'user-admin',
      password: 'admin123',
      role: 'admin' as const,
      email: '<EMAIL>',
      workspaceIds: []
    },
    'operator1': {
      id: 'user-op1',
      password: 'op123',
      role: 'operator' as const,
      email: '<EMAIL>',
      workspaceIds: []
    },
    'operator2': {
      id: 'user-op2',
      password: 'op123',
      role: 'operator' as const,
      email: '<EMAIL>',
      workspaceIds: []
    }
  };

  // 登录
  async login(username: string, password: string): Promise<SimpleAuthState> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const user = this.users[username as keyof typeof this.users];
    if (!user || user.password !== password) {
      throw new Error('用户名或密码错误');
    }

    // 加载用户的工作区分配
    const savedUsers = this.loadUsers();
    const userData = savedUsers.find(u => u.username === username) || {
      id: user.id,
      username,
      email: user.email,
      role: user.role,
      workspaceIds: user.workspaceIds
    };

    const authState: SimpleAuthState = {
      isAuthenticated: true,
      user: userData
    };

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(authState));
    return authState;
  }

  // 登出
  logout(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  // 获取当前认证状态
  getAuthState(): SimpleAuthState {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to parse auth state:', error);
    }

    return {
      isAuthenticated: false,
      user: null
    };
  }

  // 检查是否已认证
  isAuthenticated(): boolean {
    return this.getAuthState().isAuthenticated;
  }

  // 获取当前用户
  getCurrentUser(): SimpleUser | null {
    return this.getAuthState().user;
  }

  // 检查是否是管理员
  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'admin';
  }

  // 加载用户数据
  private loadUsers(): SimpleUser[] {
    try {
      const stored = localStorage.getItem('users_data');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load users data:', error);
    }

    // 返回默认用户
    return Object.entries(this.users).map(([username, userData]) => ({
      id: userData.id,
      username,
      email: userData.email,
      role: userData.role,
      workspaceIds: userData.workspaceIds
    }));
  }

  // 保存用户数据
  private saveUsers(users: SimpleUser[]): void {
    localStorage.setItem('users_data', JSON.stringify(users));
  }

  // 获取所有用户
  getUsers(): SimpleUser[] {
    return this.loadUsers();
  }

  // 为用户分配工作区
  assignWorkspaceToUser(userId: string, workspaceId: string): void {
    const users = this.loadUsers();
    const user = users.find(u => u.id === userId);
    if (user && !user.workspaceIds.includes(workspaceId)) {
      user.workspaceIds.push(workspaceId);
      this.saveUsers(users);
    }
  }

  // 移除用户的工作区权限
  removeWorkspaceFromUser(userId: string, workspaceId: string): void {
    const users = this.loadUsers();
    const user = users.find(u => u.id === userId);
    if (user) {
      user.workspaceIds = user.workspaceIds.filter(id => id !== workspaceId);
      this.saveUsers(users);
    }
  }

  // 检查用户是否有工作区权限
  hasWorkspaceAccess(userId: string, workspaceId: string): boolean {
    const users = this.loadUsers();
    const user = users.find(u => u.id === userId);
    return user?.role === 'admin' || user?.workspaceIds.includes(workspaceId) || false;
  }

  // 创建新用户
  createUser(userData: {
    username: string;
    password: string;
    email?: string;
    role: 'admin' | 'operator';
    workspaceIds?: string[];
  }): boolean {
    try {
      const users = this.loadUsers();

      // 检查用户名是否已存在
      if (users.find(u => u.username === userData.username)) {
        throw new Error('用户名已存在');
      }

      // 检查用户名是否在内存数据库中已存在
      if (this.users[userData.username]) {
        throw new Error('用户名已存在');
      }

      const newUser: SimpleUser = {
        id: `user-${Date.now()}`,
        username: userData.username,
        email: userData.email,
        role: userData.role,
        workspaceIds: userData.workspaceIds || []
      };

      // 添加到内存数据库（用于登录验证）
      this.users[userData.username] = {
        id: newUser.id,
        password: userData.password,
        role: userData.role,
        email: userData.email,
        workspaceIds: userData.workspaceIds || []
      };

      // 添加到持久化用户列表
      users.push(newUser);
      this.saveUsers(users);

      console.log('User created successfully:', newUser.username);
      console.log('User data:', newUser);
      console.log('Memory database entry:', this.users[userData.username]);

      return true;
    } catch (error) {
      console.error('Failed to create user:', error);
      throw error;
    }
  }

  // 更新用户信息
  updateUser(userId: string, userData: {
    username?: string;
    password?: string;
    email?: string;
    role?: 'admin' | 'operator';
  }): boolean {
    try {
      const users = this.loadUsers();
      const userIndex = users.findIndex(u => u.id === userId);

      if (userIndex === -1) {
        throw new Error('用户不存在');
      }

      const user = users[userIndex];
      const oldUsername = user.username;

      // 如果要更新用户名，检查新用户名是否已存在
      if (userData.username && userData.username !== user.username) {
        if (users.find(u => u.username === userData.username && u.id !== userId)) {
          throw new Error('用户名已存在');
        }
      }

      // 更新用户信息
      if (userData.username) user.username = userData.username;
      if (userData.email !== undefined) user.email = userData.email;
      if (userData.role) user.role = userData.role;

      // 更新内存中的用户数据库
      if (userData.username && userData.username !== oldUsername) {
        // 如果用户名改变了，需要更新内存数据库的键
        delete this.users[oldUsername];
        this.users[userData.username] = {
          ...this.users[oldUsername] || {},
          id: user.id,
          role: user.role,
          email: user.email,
          workspaceIds: user.workspaceIds
        };

        if (userData.password) {
          this.users[userData.username].password = userData.password;
        }
      } else {
        // 只更新现有用户的信息
        if (this.users[user.username]) {
          this.users[user.username].role = user.role;
          this.users[user.username].email = user.email;
          if (userData.password) {
            this.users[user.username].password = userData.password;
          }
        }
      }

      users[userIndex] = user;
      this.saveUsers(users);

      console.log('User updated successfully:', user.username);
      return true;
    } catch (error) {
      console.error('Failed to update user:', error);
      throw error;
    }
  }

  // 删除用户
  deleteUser(userId: string): boolean {
    try {
      const users = this.loadUsers();
      const user = users.find(u => u.id === userId);

      if (!user) {
        throw new Error('用户不存在');
      }

      // 不能删除当前登录的用户
      const currentUser = this.getCurrentUser();
      if (currentUser && currentUser.id === userId) {
        throw new Error('不能删除当前登录的用户');
      }

      // 从用户列表中移除
      const filteredUsers = users.filter(u => u.id !== userId);
      this.saveUsers(filteredUsers);

      // 从内存数据库中移除
      delete this.users[user.username];

      // 移除用户的所有工作区分配
      user.workspaceIds.forEach(workspaceId => {
        this.removeWorkspaceFromUser(userId, workspaceId);
      });

      console.log('User deleted successfully:', user.username);
      return true;
    } catch (error) {
      console.error('Failed to delete user:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const simpleAuthService = new SimpleAuthService();
