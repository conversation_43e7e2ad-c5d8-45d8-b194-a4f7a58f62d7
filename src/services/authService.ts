import { 
  User, 
  LoginCredentials, 
  AuthState, 
  AuditLog, 
  DEFAULT_USERS,
  UserRole,
  Permission,
  ROLE_PERMISSIONS
} from '../types/auth';

class AuthService {
  private readonly STORAGE_KEYS = {
    AUTH_STATE: 'kubex_auth_state',
    USERS: 'kubex_users',
    AUDIT_LOGS: 'kubex_audit_logs'
  };

  // 默认密码（实际项目中应该使用加密存储）
  private readonly DEFAULT_PASSWORDS: Record<string, string> = {
    'admin': 'admin123',
    'operator1': 'op123',
    'operator2': 'op123'
  };

  constructor() {
    this.initializeDefaultUsers();
  }

  // 初始化默认用户
  private initializeDefaultUsers(): void {
    const existingUsers = this.getUsers();
    if (existingUsers.length === 0) {
      localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify(DEFAULT_USERS));
    }
  }

  // 登录
  async login(credentials: LoginCredentials): Promise<AuthState> {
    const { username, password } = credentials;
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 验证用户名和密码
    const users = this.getUsers();
    const user = users.find(u => u.username === username && u.isActive);
    
    if (!user || this.DEFAULT_PASSWORDS[username] !== password) {
      throw new Error('用户名或密码错误');
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date().toISOString();
    this.updateUser(user);

    // 生成简单的token（实际项目中应该使用JWT）
    const token = btoa(`${user.id}:${Date.now()}`);
    
    const authState: AuthState = {
      isAuthenticated: true,
      user,
      token
    };

    // 保存认证状态
    localStorage.setItem(this.STORAGE_KEYS.AUTH_STATE, JSON.stringify(authState));

    // 记录登录日志
    this.logAction(user.id, '用户登录', 'auth', undefined, { username });

    return authState;
  }

  // 登出
  logout(): void {
    const authState = this.getAuthState();
    if (authState.user) {
      this.logAction(authState.user.id, '用户登出', 'auth');
    }
    
    localStorage.removeItem(this.STORAGE_KEYS.AUTH_STATE);
  }

  // 获取当前认证状态
  getAuthState(): AuthState {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.AUTH_STATE);
      if (stored) {
        const authState = JSON.parse(stored);
        // 验证token是否过期（简单实现，实际项目中应该更严格）
        if (authState.token && authState.user) {
          return authState;
        }
      }
    } catch (error) {
      console.error('Failed to parse auth state:', error);
    }

    return {
      isAuthenticated: false,
      user: null,
      token: null
    };
  }

  // 检查是否已认证
  isAuthenticated(): boolean {
    return this.getAuthState().isAuthenticated;
  }

  // 获取当前用户
  getCurrentUser(): User | null {
    return this.getAuthState().user;
  }

  // 检查权限
  hasPermission(permission: Permission): boolean {
    const user = this.getCurrentUser();
    if (!user || !user.isActive) return false;
    return user.permissions.includes(permission);
  }

  // 获取所有用户
  getUsers(): User[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.USERS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to parse users:', error);
      return [];
    }
  }

  // 创建用户
  createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'permissions'>): User {
    const users = this.getUsers();
    
    // 检查用户名是否已存在
    if (users.some(u => u.username === userData.username)) {
      throw new Error('用户名已存在');
    }

    const newUser: User = {
      ...userData,
      id: `user-${Date.now()}`,
      permissions: ROLE_PERMISSIONS[userData.role],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    users.push(newUser);
    localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify(users));

    // 记录操作日志
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      this.logAction(currentUser.id, '创建用户', 'user', newUser.id, { username: newUser.username });
    }

    return newUser;
  }

  // 更新用户
  updateUser(updatedUser: User): User {
    const users = this.getUsers();
    const index = users.findIndex(u => u.id === updatedUser.id);
    
    if (index === -1) {
      throw new Error('用户不存在');
    }

    updatedUser.updatedAt = new Date().toISOString();
    users[index] = updatedUser;
    localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify(users));

    // 如果更新的是当前用户，同时更新认证状态
    const authState = this.getAuthState();
    if (authState.user && authState.user.id === updatedUser.id) {
      const newAuthState = { ...authState, user: updatedUser };
      localStorage.setItem(this.STORAGE_KEYS.AUTH_STATE, JSON.stringify(newAuthState));
    }

    // 记录操作日志
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      this.logAction(currentUser.id, '更新用户', 'user', updatedUser.id, { username: updatedUser.username });
    }

    return updatedUser;
  }

  // 删除用户
  deleteUser(userId: string): void {
    const users = this.getUsers();
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }

    const deletedUser = users[userIndex];
    users.splice(userIndex, 1);
    localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify(users));

    // 记录操作日志
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      this.logAction(currentUser.id, '删除用户', 'user', userId, { username: deletedUser.username });
    }
  }

  // 分配工作区给用户
  assignWorkspaceToUser(userId: string, workspaceId: string): void {
    const users = this.getUsers();
    const user = users.find(u => u.id === userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }

    if (!user.workspaceIds.includes(workspaceId)) {
      user.workspaceIds.push(workspaceId);
      this.updateUser(user);

      // 记录操作日志
      const currentUser = this.getCurrentUser();
      if (currentUser) {
        this.logAction(currentUser.id, '分配工作区', 'workspace', workspaceId, { 
          targetUserId: userId, 
          targetUsername: user.username 
        });
      }
    }
  }

  // 移除用户的工作区权限
  removeWorkspaceFromUser(userId: string, workspaceId: string): void {
    const users = this.getUsers();
    const user = users.find(u => u.id === userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }

    user.workspaceIds = user.workspaceIds.filter(id => id !== workspaceId);
    this.updateUser(user);

    // 记录操作日志
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      this.logAction(currentUser.id, '移除工作区权限', 'workspace', workspaceId, { 
        targetUserId: userId, 
        targetUsername: user.username 
      });
    }
  }

  // 记录操作日志
  logAction(
    userId: string, 
    action: string, 
    resource: string, 
    resourceId?: string, 
    details?: Record<string, any>
  ): void {
    const logs = this.getAuditLogs();
    const user = this.getUsers().find(u => u.id === userId);
    
    const log: AuditLog = {
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      userId,
      username: user?.username || 'Unknown',
      action,
      resource,
      resourceId,
      details,
      timestamp: new Date().toISOString()
    };

    logs.unshift(log); // 添加到开头，最新的在前面
    
    // 只保留最近1000条日志
    if (logs.length > 1000) {
      logs.splice(1000);
    }

    localStorage.setItem(this.STORAGE_KEYS.AUDIT_LOGS, JSON.stringify(logs));
  }

  // 获取审计日志
  getAuditLogs(): AuditLog[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.AUDIT_LOGS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to parse audit logs:', error);
      return [];
    }
  }
}

// 导出单例实例
export const authService = new AuthService();
