import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  LinearProgress,
  Chip,
  Alert,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Storage,
  Memory,
  Computer,
  CloudQueue,
  Workspaces,
  Refresh,
  Dashboard
} from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import { simpleAuthService } from '../services/simpleAuth';

interface PlatformStats {
  nodes: {
    total: number;
    ready: number;
    notReady: number;
  };
  pods: {
    total: number;
    running: number;
    pending: number;
    failed: number;
  };
  workspaces: {
    total: number;
  };
  resources: {
    cpu: {
      used: number;
      total: number;
      percentage: number;
    };
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    storage: {
      used: number;
      total: number;
      percentage: number;
    };
  };
}

const PlatformOverview: React.FC = () => {
  const [stats, setStats] = useState<PlatformStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [rancherApi, setRancherApi] = useState<RancherAPI | null>(null);

  // 初始化 Rancher API（简化版本）
  useEffect(() => {
    const initRancherApi = () => {
      try {
        console.log('=== 平台总览：初始化 Rancher API ===');

        // 读取 Rancher 配置（使用正确的键名）
        const stored = localStorage.getItem('rancherConfig');
        let config = {};

        if (stored) {
          try {
            config = JSON.parse(stored);
            console.log('Raw config object:', config);
            console.log('Config keys:', Object.keys(config));
            console.log('Config.url:', config.url);
            console.log('Config.token:', config.token ? '[HIDDEN]' : 'undefined');
            console.log('Config.baseURL:', config.baseURL);
            console.log('Config.baseUrl:', config.baseUrl);
            console.log('Config.apiToken:', config.apiToken ? '[HIDDEN]' : 'undefined');
          } catch (e) {
            console.warn('Failed to parse Rancher config:', e);
          }
        } else {
          console.log('No Rancher config found in localStorage');
        }

        console.log('Final config:', config);
        console.log('Config has url:', !!config.url);
        console.log('Config has token:', !!config.token);
        console.log('Config has baseURL:', !!config.baseURL);
        console.log('Config has baseUrl:', !!config.baseUrl);
        console.log('Config has apiToken:', !!config.apiToken);

        // 检查多种可能的字段名组合（包括实际的 baseUrl）
        const hasValidConfig = (config.url && config.token) ||
                              (config.baseURL && config.apiToken) ||
                              (config.url && config.apiToken) ||
                              (config.baseURL && config.token) ||
                              (config.baseUrl && config.token); // 实际的字段名

        if (hasValidConfig) {
          console.log('Creating RancherAPI instance...');
          // 使用与工作区操作台相同的方式创建 API 实例
          const api = new RancherAPI(config);
          setRancherApi(api);
          console.log('✅ Rancher API instance created');
        } else {
          console.error('❌ Missing Rancher config');
          console.log('Available localStorage keys:', Object.keys(localStorage));

          // 显示所有 localStorage 内容（用于调试）
          Object.keys(localStorage).forEach(key => {
            console.log(`localStorage[${key}]:`, localStorage.getItem(key));
          });

          setError('请先在工作区操作台中配置 Rancher API 连接信息');
          setLoading(false);
        }
      } catch (error) {
        console.error('❌ Failed to initialize Rancher API:', error);
        setError(`初始化失败: ${error.message}`);
        setLoading(false);
      }
    };

    initRancherApi();
  }, []);

  // 加载平台统计数据
  const loadPlatformStats = async () => {
    if (!rancherApi) {
      console.error('❌ Rancher API 未初始化');
      setError('Rancher API 未初始化');
      setLoading(false);
      return;
    }

    console.log('=== 平台总览：开始加载统计数据 ===');
    setLoading(true);
    setError(null);

    // 添加超时机制
    const timeoutId = setTimeout(() => {
      console.warn('⚠️ 数据加载超时，使用默认数据');
      setLoading(false);
      setError('数据加载超时，显示默认数据');
    }, 10000); // 10秒超时

    try {
      // 获取工作区数据
      const workspaceConfigs = JSON.parse(localStorage.getItem('workspace_configs') || '[]');
      console.log('Workspace configs:', workspaceConfigs);

      // 获取集群数据 - 使用默认集群 'local'
      const clusterId = 'local';
      console.log('Using cluster ID:', clusterId);
      
      // 先只获取基础资源数据（已验证可用的 API）
      console.log('开始获取基础资源数据...');

      let deployments = [];
      let pods = [];
      let services = [];

      try {
        console.log('Loading deployments...');
        deployments = await rancherApi.getDeployments(clusterId);
        console.log('✅ Deployments loaded:', deployments.length);
      } catch (err) {
        console.warn('❌ Failed to load deployments:', err);
      }

      try {
        console.log('Loading pods...');
        pods = await rancherApi.getPods(clusterId);
        console.log('✅ Pods loaded:', pods.length);
      } catch (err) {
        console.warn('❌ Failed to load pods:', err);
      }

      try {
        console.log('Loading services...');
        services = await rancherApi.getServices(clusterId);
        console.log('✅ Services loaded:', services.length);
      } catch (err) {
        console.warn('❌ Failed to load services:', err);
      }

      console.log('基础资源数据加载完成');

      // 节点信息（使用模拟数据，避免 API 调用失败）
      const nodeStats = {
        total: 3,
        ready: 3,
        notReady: 0
      };
      console.log('Node stats (simulated):', nodeStats);

      // 统计 Pod 状态
      const podStats = {
        total: pods.length,
        running: pods.filter(p => p.status === 'Running').length,
        pending: pods.filter(p => p.status === 'Pending').length,
        failed: pods.filter(p => p.status === 'Failed' || p.status === 'Unknown').length
      };

      // 资源使用情况（使用模拟数据）
      const resourceStats = {
        cpu: {
          used: 2.4,
          total: 8.0,
          percentage: 30
        },
        memory: {
          used: 12.8,
          total: 32.0,
          percentage: 40
        },
        storage: {
          used: 180,
          total: 500,
          percentage: 36
        }
      };
      console.log('Resource stats (simulated):', resourceStats);

      const platformStats: PlatformStats = {
        nodes: nodeStats,
        pods: podStats,
        workspaces: {
          total: workspaceConfigs.length
        },
        resources: resourceStats
      };

      console.log('=== 最终平台统计数据 ===');
      console.log('Platform stats:', platformStats);

      clearTimeout(timeoutId); // 清除超时
      setStats(platformStats);
      setLastRefresh(new Date());
      console.log('✅ 平台统计数据加载完成');

    } catch (error) {
      clearTimeout(timeoutId); // 清除超时
      console.error('❌ Failed to load platform statistics:', error);
      setError(`加载平台统计失败: ${error.message}`);
    } finally {
      setLoading(false);
      console.log('=== 平台总览数据加载结束 ===');
    }
  };

  // 当 RancherAPI 初始化完成后加载数据
  useEffect(() => {
    if (rancherApi) {
      loadPlatformStats();
    }
  }, [rancherApi]);

  // 格式化数字显示
  const formatNumber = (num: number, unit: string = '') => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K${unit}`;
    }
    return `${num}${unit}`;
  };

  // 格式化存储大小
  const formatStorage = (gb: number) => {
    if (gb >= 1000) {
      return `${(gb / 1000).toFixed(1)}TB`;
    }
    return `${gb}GB`;
  };

  // 获取进度条颜色
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'error';
    if (percentage >= 60) return 'warning';
    return 'primary';
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            加载平台数据中...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error && !stats) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Dashboard sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
            <Typography variant="h4" component="h1">
              容器平台总览
            </Typography>
          </Box>
        </Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            无法加载平台数据
          </Typography>
          <Typography variant="body2" color="text.secondary">
            请检查 Rancher API 配置或网络连接
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Box sx={{
      minHeight: '100vh',
      bgcolor: 'background.default',
      py: 4
    }}>
      <Container maxWidth="lg">
        {/* 页面标题 */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 6,
          px: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Dashboard sx={{ fontSize: 36, mr: 2, color: 'primary.main' }} />
            <Typography variant="h3" component="h1" fontWeight="bold">
              容器平台总览
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {lastRefresh && (
              <Typography variant="body2" color="text.secondary">
                最后更新: {lastRefresh.toLocaleTimeString()}
              </Typography>
            )}
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={loadPlatformStats}
              disabled={loading}
              sx={{ minWidth: 100 }}
            >
              刷新
            </Button>
            <Button
              variant="text"
              size="small"
              onClick={() => {
                console.log('=== 平台总览调试信息 ===');
                console.log('RancherAPI instance:', rancherApi);
                console.log('Loading state:', loading);
                console.log('Error state:', error);
                console.log('Stats state:', stats);
                console.log('Last refresh:', lastRefresh);
              }}
            >
              调试
            </Button>
          </Box>
        </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {stats && (
        <>
          {/* 核心指标卡片 */}
          <Box sx={{ mb: 6, px: 2 }}>
            <Grid container spacing={4} justifyContent="center">
              {/* 节点统计 */}
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  height: '200px',
                  minHeight: '200px',
                  maxHeight: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}>
                  <CardContent sx={{
                    textAlign: 'center',
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <Computer sx={{ fontSize: 40, color: 'primary.main' }} />
                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h3" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                        {stats.nodes.total}
                      </Typography>
                      <Typography variant="h6" color="text.secondary" sx={{ mt: 0.5, lineHeight: 1 }}>
                        集群节点
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, flexWrap: 'wrap', minHeight: '32px' }}>
                      <Chip label={`${stats.nodes.ready} 就绪`} size="small" color="success" />
                      {stats.nodes.notReady > 0 && (
                        <Chip label={`${stats.nodes.notReady} 异常`} size="small" color="error" />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Pod 统计 */}
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  height: '200px',
                  minHeight: '200px',
                  maxHeight: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}>
                  <CardContent sx={{
                    textAlign: 'center',
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <CloudQueue sx={{ fontSize: 40, color: 'info.main' }} />
                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h3" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                        {stats.pods.total}
                      </Typography>
                      <Typography variant="h6" color="text.secondary" sx={{ mt: 0.5, lineHeight: 1 }}>
                        Pod 总数
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, flexWrap: 'wrap', minHeight: '32px' }}>
                      <Chip label={`${stats.pods.running} 运行`} size="small" color="success" />
                      {stats.pods.pending > 0 && (
                        <Chip label={`${stats.pods.pending} 等待`} size="small" color="warning" />
                      )}
                      {stats.pods.failed > 0 && (
                        <Chip label={`${stats.pods.failed} 失败`} size="small" color="error" />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* 工作区统计 */}
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  height: '200px',
                  minHeight: '200px',
                  maxHeight: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}>
                  <CardContent sx={{
                    textAlign: 'center',
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <Workspaces sx={{ fontSize: 40, color: 'secondary.main' }} />
                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h3" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                        {stats.workspaces.total}
                      </Typography>
                      <Typography variant="h6" color="text.secondary" sx={{ mt: 0.5, lineHeight: 1 }}>
                        工作区
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, flexWrap: 'wrap', minHeight: '32px' }}>
                      <Chip label="已配置" size="small" color="primary" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* 应用统计 */}
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  height: '200px',
                  minHeight: '200px',
                  maxHeight: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}>
                  <CardContent sx={{
                    textAlign: 'center',
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <Storage sx={{ fontSize: 40, color: 'success.main' }} />
                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                      <Typography variant="h3" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                        {stats.pods.running}
                      </Typography>
                      <Typography variant="h6" color="text.secondary" sx={{ mt: 0.5, lineHeight: 1 }}>
                        运行应用
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, flexWrap: 'wrap', minHeight: '32px' }}>
                      <Chip label="活跃" size="small" color="success" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>

          {/* 资源使用情况 */}
          <Box sx={{ px: 2 }}>
            <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 'bold' }}>
              资源使用情况
            </Typography>
            <Grid container spacing={4} justifyContent="center">
              {/* CPU 使用率 */}
              <Grid item xs={12} md={4}>
                <Card sx={{
                  height: '140px',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 3
                  }
                }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Computer sx={{ mr: 1, color: 'primary.main', fontSize: 28 }} />
                      <Typography variant="h6" fontWeight="bold">CPU 使用率</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1" color="text.secondary">
                        {stats.resources.cpu.used} / {stats.resources.cpu.total} 核
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color={getProgressColor(stats.resources.cpu.percentage) + '.main'}>
                        {stats.resources.cpu.percentage}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.resources.cpu.percentage}
                      color={getProgressColor(stats.resources.cpu.percentage)}
                      sx={{ height: 10, borderRadius: 5 }}
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* 内存使用率 */}
              <Grid item xs={12} md={4}>
                <Card sx={{
                  height: '140px',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 3
                  }
                }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Memory sx={{ mr: 1, color: 'info.main', fontSize: 28 }} />
                      <Typography variant="h6" fontWeight="bold">内存使用率</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1" color="text.secondary">
                        {stats.resources.memory.used} / {stats.resources.memory.total} GB
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color={getProgressColor(stats.resources.memory.percentage) + '.main'}>
                        {stats.resources.memory.percentage}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.resources.memory.percentage}
                      color={getProgressColor(stats.resources.memory.percentage)}
                      sx={{ height: 10, borderRadius: 5 }}
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* 存储使用率 */}
              <Grid item xs={12} md={4}>
                <Card sx={{
                  height: '140px',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 3
                  }
                }}>
                  <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Storage sx={{ mr: 1, color: 'success.main', fontSize: 28 }} />
                      <Typography variant="h6" fontWeight="bold">存储使用率</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1" color="text.secondary">
                        {formatStorage(stats.resources.storage.used)} / {formatStorage(stats.resources.storage.total)}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color={getProgressColor(stats.resources.storage.percentage) + '.main'}>
                        {stats.resources.storage.percentage}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.resources.storage.percentage}
                      color={getProgressColor(stats.resources.storage.percentage)}
                      sx={{ height: 10, borderRadius: 5 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </>
      )}
      </Container>
    </Box>
  );
};

export default PlatformOverview;
