import React, { useState, useEffect } from 'react';
import {
  Con<PERSON>er,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Paper,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  Settings,
  Dashboard as DashboardIcon,
  Apps,
  CloudUpload,
  Monitor
} from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import type { RancherConfig, WorkspaceConfig } from '../services/rancherApi';
import ConfigDialog from '../components/ConfigDialog';
import { authService } from '../services/authService';
import { Permission, hasPermission, canAccessWorkspace, getAccessibleWorkspaceIds } from '../types/auth';

const MainDashboard: React.FC = () => {
  const [config, setConfig] = useState<RancherConfig | null>(null);
  const [api, setApi] = useState<RancherAPI | null>(null);
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>('');
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configOpen, setConfigOpen] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('未连接');

  // 获取当前用户
  const currentUser = authService.getCurrentUser();

  // 获取用户可访问的工作区
  const accessibleWorkspaces = workspaces.filter(workspace =>
    canAccessWorkspace(currentUser, workspace.id)
  );

  useEffect(() => {
    const savedConfig = localStorage.getItem('rancherConfig');
    if (savedConfig) {
      const parsedConfig: RancherConfig = JSON.parse(savedConfig);
      setConfig(parsedConfig);
      const rancherApi = new RancherAPI(parsedConfig);
      setApi(rancherApi);
      testConnection(rancherApi);
    }

    // 加载保存的工作区
    const savedWorkspaces = localStorage.getItem('workspaces');
    if (savedWorkspaces) {
      const parsedWorkspaces: WorkspaceConfig[] = JSON.parse(savedWorkspaces);
      setWorkspaces(parsedWorkspaces);

      // 根据用户权限过滤可访问的工作区
      const accessible = parsedWorkspaces.filter(workspace =>
        canAccessWorkspace(currentUser, workspace.id)
      );

      if (accessible.length > 0) {
        setSelectedWorkspace(accessible[0].id);
      }
    }
  }, []);

  const testConnection = async (rancherApi: RancherAPI) => {
    setLoading(true);
    try {
      const connected = await rancherApi.testConnection();
      setConnectionStatus(connected ? '已连接' : '连接失败');
    } catch (err) {
      setConnectionStatus('连接错误');
      setError(`连接测试失败: ${err.message}`);
    }
    setLoading(false);
  };

  const handleConfigSave = async (newConfig: RancherConfig) => {
    setLoading(true);
    setError(null);
    
    const rancherApi = new RancherAPI(newConfig);
    const connected = await rancherApi.testConnection();
    
    if (connected) {
      setConfig(newConfig);
      setApi(rancherApi);
      localStorage.setItem('rancherConfig', JSON.stringify(newConfig));
      setConfigOpen(false);
      setConnectionStatus('已连接');
    } else {
      setError('连接失败，请检查 URL 和 Token');
      setConnectionStatus('连接失败');
    }
    setLoading(false);
  };

  const currentWorkspace = workspaces.find(w => w.id === selectedWorkspace);

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Kubernetes 操作面板
        </Typography>
        <Button
          variant="contained"
          onClick={() => setConfigOpen(true)}
          startIcon={<Settings />}
        >
          配置连接
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {!config ? (
        <Alert severity="info">请先配置 Rancher API 连接。</Alert>
      ) : (
        <>
          {/* 连接状态和工作区选择 */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">连接状态</Typography>
              <Typography 
                variant="body1" 
                color={connectionStatus === '已连接' ? 'success.main' : 'error.main'}
              >
                {connectionStatus}
              </Typography>
            </Box>

            {connectionStatus === '已连接' && (
              <Box>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="workspace-select-label">选择工作区</InputLabel>
                  <Select
                    labelId="workspace-select-label"
                    value={selectedWorkspace}
                    label="选择工作区"
                    onChange={(e) => setSelectedWorkspace(e.target.value)}
                    disabled={loading}
                  >
                    {accessibleWorkspaces.map((workspace) => (
                      <MenuItem key={workspace.id} value={workspace.id}>
                        {workspace.name} ({workspace.namespaces.length} 个命名空间)
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {accessibleWorkspaces.length === 0 && (
                  <Alert severity="warning">
                    {workspaces.length === 0
                      ? '没有找到工作区。请联系管理员创建工作区。'
                      : '您没有被分配到任何工作区。请联系管理员分配工作区权限。'
                    }
                  </Alert>
                )}

                {currentWorkspace && (
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      当前工作区: {currentWorkspace.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      包含命名空间: {currentWorkspace.namespaces.join(', ')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      用户角色: {currentUser?.role === 'admin' ? '管理员' : '操作员'}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
          </Paper>

          {/* 功能标签页 */}
          {selectedWorkspace && connectionStatus === '已连接' && (
            <>
              <Paper sx={{ mb: 3 }}>
                <Tabs
                  value={currentTab}
                  onChange={(_, newValue) => setCurrentTab(newValue)}
                  variant="fullWidth"
                >
                  <Tab icon={<DashboardIcon />} label="概览" />
                  <Tab icon={<Monitor />} label="监控" />
                  <Tab icon={<Apps />} label="应用管理" />
                  <Tab icon={<CloudUpload />} label="应用部署" />
                </Tabs>
              </Paper>

              {loading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress />
                </Box>
              )}

              {/* 标签页内容 */}
              <Paper sx={{ p: 3 }}>
                {currentTab === 0 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>概览</Typography>
                    <Typography variant="body1" gutterBottom>
                      工作区: {currentWorkspace?.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      这里将显示工作区的资源概览和监控信息
                    </Typography>
                    <Alert severity="info" sx={{ mt: 2 }}>
                      功能正在开发中，敬请期待！
                    </Alert>
                  </Box>
                )}

                {currentTab === 1 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>资源监控</Typography>
                    <Typography variant="body2" color="text.secondary">
                      这里将显示 CPU、内存使用率和 Pod 状态监控
                    </Typography>
                    <Alert severity="info" sx={{ mt: 2 }}>
                      功能正在开发中，敬请期待！
                    </Alert>
                  </Box>
                )}

                {currentTab === 2 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>应用管理</Typography>
                    <Typography variant="body2" color="text.secondary">
                      这里将显示应用列表，支持启动、停止、重启、扩缩容等操作
                    </Typography>
                    <Alert severity="info" sx={{ mt: 2 }}>
                      功能正在开发中，敬请期待！
                    </Alert>
                  </Box>
                )}

                {currentTab === 3 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>应用部署</Typography>
                    <Typography variant="body2" color="text.secondary">
                      这里将提供快速部署容器应用的界面
                    </Typography>
                    <Alert severity="info" sx={{ mt: 2 }}>
                      功能正在开发中，敬请期待！
                    </Alert>
                  </Box>
                )}
              </Paper>
            </>
          )}
        </>
      )}

      <ConfigDialog
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        onSave={handleConfigSave}
        loading={loading}
        error={error}
        currentConfig={config}
      />
    </Container>
  );
};

export default MainDashboard;
