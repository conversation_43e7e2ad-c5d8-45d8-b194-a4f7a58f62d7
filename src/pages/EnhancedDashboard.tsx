import React, { useState, useEffect, useCallback } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Button, 
  Container, 
  Alert, 
  Box,
  Paper,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import { Settings, Dashboard as DashboardIcon, Apps, CloudUpload, Monitor } from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import type {
  RancherConfig,
  Namespace,
  WorkspaceConfig,
  Deployment,
  Pod,
  ResourceMetrics
} from '../services/rancherApi';
import ConfigDialog from '../components/ConfigDialog';
import ResourceMonitor from '../components/ResourceMonitor';
import ApplicationManager from '../components/ApplicationManager';
import ApplicationDeployment from '../components/ApplicationDeployment';

const EnhancedDashboard: React.FC = () => {
  const [config, setConfig] = useState<RancherConfig | null>(null);
  const [api, setApi] = useState<RancherAPI | null>(null);
  const [clusters, setClusters] = useState<any[]>([]);
  const [selectedCluster, setSelectedCluster] = useState<string>('');
  const [namespaces, setNamespaces] = useState<Namespace[]>([]);
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>('');
  const [currentTab, setCurrentTab] = useState(0);

  // 资源数据
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [pods, setPods] = useState<Pod[]>([]);
  const [metrics, setMetrics] = useState<ResourceMetrics>({
    cpu: { used: '0m', total: '0m', percentage: 0 },
    memory: { used: '0Mi', total: '0Mi', percentage: 0 },
    pods: { running: 0, pending: 0, failed: 0, total: 0 }
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configOpen, setConfigOpen] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('未连接');

  useEffect(() => {
    const savedConfig = localStorage.getItem('rancherConfig');
    if (savedConfig) {
      const parsedConfig: RancherConfig = JSON.parse(savedConfig);
      setConfig(parsedConfig);
      const rancherApi = new RancherAPI(parsedConfig);
      setApi(rancherApi);
      testConnection(rancherApi);
    }

    // 加载保存的工作区
    const savedWorkspaces = localStorage.getItem('workspaces');
    if (savedWorkspaces) {
      const parsedWorkspaces: WorkspaceConfig[] = JSON.parse(savedWorkspaces);
      setWorkspaces(parsedWorkspaces);
      if (parsedWorkspaces.length > 0) {
        setSelectedWorkspace(parsedWorkspaces[0].id);
      }
    }
  }, []);

  const testConnection = async (rancherApi: RancherAPI) => {
    setLoading(true);
    try {
      const connected = await rancherApi.testConnection();
      setConnectionStatus(connected ? '已连接' : '连接失败');
      if (connected) {
        loadClusters(rancherApi);
      }
    } catch (err) {
      setConnectionStatus('连接错误');
      setError(`连接测试失败: ${err.message}`);
    }
    setLoading(false);
  };

  const loadClusters = useCallback(async (rancherApi: RancherAPI) => {
    try {
      const clusterData = await rancherApi.getClusters();
      setClusters(clusterData);
      if (clusterData.length > 0 && !selectedCluster) {
        setSelectedCluster(clusterData[0].id);
        // 加载命名空间
        const namespaceData = await rancherApi.getNamespaces(clusterData[0].id);
        setNamespaces(namespaceData);
      }
    } catch (e) {
      setError('Failed to load clusters.');
    }
  }, [selectedCluster]);

  const handleConfigSave = async (newConfig: RancherConfig) => {
    setLoading(true);
    setError(null);
    
    const rancherApi = new RancherAPI(newConfig);
    const connected = await rancherApi.testConnection();
    
    if (connected) {
      setConfig(newConfig);
      setApi(rancherApi);
      localStorage.setItem('rancherConfig', JSON.stringify(newConfig));
      setConfigOpen(false);
      setConnectionStatus('已连接');
      loadClusters(rancherApi);
    } else {
      setError('连接失败，请检查 URL 和 Token');
      setConnectionStatus('连接失败');
    }
    setLoading(false);
  };

  const loadWorkspaceData = useCallback(async () => {
    if (!api || !selectedCluster || !selectedWorkspace) return;

    setLoading(true);
    try {
      const currentWorkspace = workspaces.find(w => w.id === selectedWorkspace);
      if (!currentWorkspace) return;

      // 加载部署
      const allDeployments = await api.getDeployments(selectedCluster);
      const workspaceDeployments = allDeployments.filter(d =>
        currentWorkspace.namespaces.includes(d.namespace)
      );
      setDeployments(workspaceDeployments);

      // 加载 Pods
      const allPods: Pod[] = [];
      for (const namespace of currentWorkspace.namespaces) {
        try {
          const namespacePods = await api.getPods(selectedCluster, namespace);
          allPods.push(...namespacePods);
        } catch (e) {
          console.warn(`Failed to load pods for namespace ${namespace}:`, e);
        }
      }
      setPods(allPods);

      // 加载资源指标
      const resourceMetrics = await api.getResourceMetrics(selectedCluster);
      setMetrics(resourceMetrics);

    } catch (e) {
      setError('Failed to load workspace data.');
      console.error('Load workspace data error:', e);
    }
    setLoading(false);
  }, [api, selectedCluster, selectedWorkspace, workspaces]);

  const handleCreateWorkspace = () => {
    // 创建一个示例工作区
    const newWorkspace: WorkspaceConfig = {
      id: `workspace-${Date.now()}`,
      name: '默认工作区',
      description: '包含常用命名空间的工作区',
      namespaces: namespaces.slice(0, 3).map(ns => ns.name), // 取前3个命名空间
      created: new Date().toISOString(),
      labels: { 'app.kubernetes.io/managed-by': 'rancher-dashboard' }
    };

    const updatedWorkspaces = [...workspaces, newWorkspace];
    setWorkspaces(updatedWorkspaces);
    localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));
    setSelectedWorkspace(newWorkspace.id);
  };

  // 当工作区改变时加载数据
  useEffect(() => {
    if (selectedWorkspace && api && selectedCluster) {
      loadWorkspaceData();
    }
  }, [selectedWorkspace, api, selectedCluster, loadWorkspaceData]);

  // 应用管理函数
  const handleAppStart = async (namespace: string, name: string, replicas: number = 1) => {
    if (!api || !selectedCluster) return;
    await api.startDeployment(selectedCluster, namespace, name, replicas);
    loadWorkspaceData();
  };

  const handleAppStop = async (namespace: string, name: string) => {
    if (!api || !selectedCluster) return;
    await api.stopDeployment(selectedCluster, namespace, name);
    loadWorkspaceData();
  };

  const handleAppDelete = async (namespace: string, name: string) => {
    if (!api || !selectedCluster) return;
    await api.deleteDeployment(selectedCluster, namespace, name);
    loadWorkspaceData();
  };

  const handleAppRestart = async (namespace: string, name: string) => {
    if (!api || !selectedCluster) return;
    await api.restartDeployment(selectedCluster, namespace, name);
    loadWorkspaceData();
  };

  const handleAppScale = async (namespace: string, name: string, replicas: number) => {
    if (!api || !selectedCluster) return;
    await api.scaleDeployment(selectedCluster, namespace, name, replicas);
    loadWorkspaceData();
  };

  const handleAppDeploy = async (config: any) => {
    if (!api || !selectedCluster) return;
    await api.deployApplication(selectedCluster, config.namespace, {
      name: config.name,
      image: config.image,
      replicas: config.replicas,
      ports: config.ports,
      env: config.env,
      resources: config.resources,
      labels: config.labels
    });
    loadWorkspaceData();
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Kubernetes 管理平台
        </Typography>
        <Button
          variant="contained"
          onClick={() => setConfigOpen(true)}
          startIcon={<Settings />}
        >
          配置连接
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {!config ? (
        <Alert severity="info">请先配置 Rancher API 连接以开始使用。</Alert>
      ) : (
        <>
          {/* 连接状态 */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              连接状态
            </Typography>
            <Typography variant="body1" color={connectionStatus === '已连接' ? 'success.main' : 'error.main'}>
              {connectionStatus}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              集群数量: {clusters.length} | 命名空间数量: {namespaces.length} | 工作区数量: {workspaces.length}
            </Typography>
          </Paper>

          {connectionStatus === '已连接' && (
            <>
              {/* 工作区管理 */}
              <Paper sx={{ p: 3, mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">工作区管理</Typography>
                  {namespaces.length > 0 && (
                    <Button 
                      variant="outlined" 
                      onClick={handleCreateWorkspace}
                      disabled={loading}
                    >
                      创建示例工作区
                    </Button>
                  )}
                </Box>
                
                {workspaces.length === 0 ? (
                  <Alert severity="info">
                    请先创建一个工作区来管理您的应用。工作区将命名空间分组，便于项目管理。
                  </Alert>
                ) : (
                  <Typography variant="body2">
                    当前工作区: {workspaces.find(w => w.id === selectedWorkspace)?.name || '未选择'}
                  </Typography>
                )}
              </Paper>

              {/* 功能标签页 */}
              {selectedWorkspace && (
                <>
                  <Paper sx={{ mb: 3 }}>
                    <Tabs
                      value={currentTab}
                      onChange={(_, newValue) => setCurrentTab(newValue)}
                      variant="fullWidth"
                    >
                      <Tab icon={<DashboardIcon />} label="概览" />
                      <Tab icon={<Monitor />} label="监控" />
                      <Tab icon={<Apps />} label="应用管理" />
                      <Tab icon={<CloudUpload />} label="应用部署" />
                    </Tabs>
                  </Paper>

                  {loading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                      <CircularProgress />
                    </Box>
                  )}

                  {/* 标签页内容 */}
                  {currentTab === 0 && (
                    <Paper sx={{ p: 3 }}>
                      <Typography variant="h6" gutterBottom>工作区概览</Typography>
                      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                        <Paper sx={{ p: 2, flex: 1 }}>
                          <Typography variant="subtitle2">应用数量</Typography>
                          <Typography variant="h4">{deployments.length}</Typography>
                        </Paper>
                        <Paper sx={{ p: 2, flex: 1 }}>
                          <Typography variant="subtitle2">Pod 数量</Typography>
                          <Typography variant="h4">{pods.length}</Typography>
                        </Paper>
                        <Paper sx={{ p: 2, flex: 1 }}>
                          <Typography variant="subtitle2">运行中 Pod</Typography>
                          <Typography variant="h4" color="success.main">
                            {metrics.pods.running}
                          </Typography>
                        </Paper>
                      </Box>
                      <ResourceMonitor
                        metrics={metrics}
                        pods={pods}
                        onRefresh={loadWorkspaceData}
                        loading={loading}
                      />
                    </Paper>
                  )}

                  {currentTab === 1 && (
                    <ResourceMonitor
                      metrics={metrics}
                      pods={pods}
                      onRefresh={loadWorkspaceData}
                      loading={loading}
                    />
                  )}

                  {currentTab === 2 && (
                    <ApplicationManager
                      deployments={deployments}
                      onStart={handleAppStart}
                      onStop={handleAppStop}
                      onDelete={handleAppDelete}
                      onRestart={handleAppRestart}
                      onScale={handleAppScale}
                      loading={loading}
                    />
                  )}

                  {currentTab === 3 && (
                    <ApplicationDeployment
                      namespaces={workspaces.find(w => w.id === selectedWorkspace)?.namespaces || []}
                      onDeploy={handleAppDeploy}
                      loading={loading}
                    />
                  )}
                </>
              )}
            </>
          )}
        </>
      )}

      <ConfigDialog
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        onSave={handleConfigSave}
        loading={loading}
        error={error}
        currentConfig={config}
      />
    </Container>
  );
};

export default EnhancedDashboard;
