import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Chip
} from '@mui/material';
import {
  Login as LoginIcon,
  Security,
  AdminPanelSettings,
  Engineering
} from '@mui/icons-material';
import { authService } from '../services/authService';
import type { LoginCredentials } from '../types/auth';

interface Props {
  onLoginSuccess: () => void;
}

const LoginPage: React.FC<Props> = ({ onLoginSuccess }) => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await authService.login(credentials);
      onLoginSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async (username: string, password: string) => {
    setCredentials({ username, password });
    setLoading(true);
    setError(null);

    try {
      await authService.login({ username, password });
      onLoginSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      py: 4
    }}>
      <Box sx={{ width: '100%' }}>
        {/* 标题 */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Security sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          <Typography variant="h3" component="h1" gutterBottom>
            KubeX
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Kubernetes 管理平台
          </Typography>
        </Box>

        {/* 登录表单 */}
        <Paper elevation={3} sx={{ p: 4, mb: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ textAlign: 'center' }}>
            用户登录
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="用户名"
              variant="outlined"
              margin="normal"
              required
              value={credentials.username}
              onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
              disabled={loading}
            />

            <TextField
              fullWidth
              label="密码"
              type="password"
              variant="outlined"
              margin="normal"
              required
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              disabled={loading}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Box>
        </Paper>

        {/* 演示账户 */}
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
            演示账户
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mb: 3 }}>
            点击下方按钮快速登录演示账户
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* 管理员账户 */}
            <Card variant="outlined">
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AdminPanelSettings color="primary" />
                    <Box>
                      <Typography variant="subtitle2">管理员账户</Typography>
                      <Typography variant="body2" color="text.secondary">
                        admin / admin123
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip label="全部权限" color="primary" size="small" />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleDemoLogin('admin', 'admin123')}
                      disabled={loading}
                    >
                      登录
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>

            {/* 操作员账户1 */}
            <Card variant="outlined">
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Engineering color="secondary" />
                    <Box>
                      <Typography variant="subtitle2">操作员账户 1</Typography>
                      <Typography variant="body2" color="text.secondary">
                        operator1 / op123
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip label="限定权限" color="secondary" size="small" />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleDemoLogin('operator1', 'op123')}
                      disabled={loading}
                    >
                      登录
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>

            {/* 操作员账户2 */}
            <Card variant="outlined">
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Engineering color="secondary" />
                    <Box>
                      <Typography variant="subtitle2">操作员账户 2</Typography>
                      <Typography variant="body2" color="text.secondary">
                        operator2 / op123
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip label="限定权限" color="secondary" size="small" />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleDemoLogin('operator2', 'op123')}
                      disabled={loading}
                    >
                      登录
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
            <strong>权限说明:</strong><br/>
            • 管理员: 可以管理所有工作区、用户和系统配置<br/>
            • 操作员: 只能访问分配给自己的工作区，进行应用管理操作
          </Typography>
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginPage;
