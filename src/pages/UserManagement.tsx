import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Person,
  AdminPanelSettings,
  Engineering,
  Assignment
} from '@mui/icons-material';
import { authService } from '../services/authService';
import type { User, UserRole } from '../types/auth';
import type { WorkspaceConfig } from '../services/rancherApi';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 用户编辑对话框
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [userForm, setUserForm] = useState({
    username: '',
    email: '',
    role: UserRole.OPERATOR as UserRole,
    workspaceIds: [] as string[],
    isActive: true
  });

  // 工作区分配对话框
  const [workspaceDialogOpen, setWorkspaceDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setUsers(authService.getUsers());
    
    // 加载工作区
    const savedWorkspaces = localStorage.getItem('workspaces');
    if (savedWorkspaces) {
      setWorkspaces(JSON.parse(savedWorkspaces));
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setUserForm({
      username: '',
      email: '',
      role: UserRole.OPERATOR,
      workspaceIds: [],
      isActive: true
    });
    setUserDialogOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setUserForm({
      username: user.username,
      email: user.email || '',
      role: user.role,
      workspaceIds: user.workspaceIds,
      isActive: user.isActive
    });
    setUserDialogOpen(true);
  };

  const handleSaveUser = async () => {
    setLoading(true);
    setError(null);

    try {
      if (editingUser) {
        // 更新用户
        const updatedUser: User = {
          ...editingUser,
          username: userForm.username,
          email: userForm.email,
          role: userForm.role,
          workspaceIds: userForm.workspaceIds,
          isActive: userForm.isActive
        };
        authService.updateUser(updatedUser);
      } else {
        // 创建新用户
        authService.createUser({
          username: userForm.username,
          email: userForm.email,
          role: userForm.role,
          workspaceIds: userForm.workspaceIds,
          isActive: userForm.isActive
        });
      }

      loadData();
      setUserDialogOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = (user: User) => {
    if (window.confirm(`确定要删除用户 "${user.username}" 吗？`)) {
      try {
        authService.deleteUser(user.id);
        loadData();
      } catch (err) {
        setError(err instanceof Error ? err.message : '删除失败');
      }
    }
  };

  const handleManageWorkspaces = (user: User) => {
    setSelectedUser(user);
    setWorkspaceDialogOpen(true);
  };

  const handleWorkspaceAssignment = (workspaceId: string, assigned: boolean) => {
    if (!selectedUser) return;

    try {
      if (assigned) {
        authService.assignWorkspaceToUser(selectedUser.id, workspaceId);
      } else {
        authService.removeWorkspaceFromUser(selectedUser.id, workspaceId);
      }
      loadData();
      // 更新选中的用户信息
      setSelectedUser(authService.getUsers().find(u => u.id === selectedUser.id) || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '操作失败');
    }
  };

  const getRoleIcon = (role: UserRole) => {
    return role === UserRole.ADMIN ? <AdminPanelSettings /> : <Engineering />;
  };

  const getRoleColor = (role: UserRole) => {
    return role === UserRole.ADMIN ? 'primary' : 'secondary';
  };

  const getRoleLabel = (role: UserRole) => {
    return role === UserRole.ADMIN ? '管理员' : '操作员';
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            用户管理
          </Typography>
          <Typography variant="body1" color="text.secondary">
            管理系统用户和权限分配
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleCreateUser}
        >
          创建用户
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 用户统计 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Person sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4">{users.length}</Typography>
            <Typography variant="body2" color="text.secondary">总用户数</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <AdminPanelSettings sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4">
              {users.filter(u => u.role === UserRole.ADMIN).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">管理员</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Engineering sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
            <Typography variant="h4">
              {users.filter(u => u.role === UserRole.OPERATOR).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">操作员</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* 用户列表 */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>用户</TableCell>
              <TableCell>角色</TableCell>
              <TableCell>工作区</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>最后登录</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getRoleIcon(user.role)}
                    <Box>
                      <Typography variant="subtitle2">{user.username}</Typography>
                      {user.email && (
                        <Typography variant="caption" color="text.secondary">
                          {user.email}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getRoleLabel(user.role)}
                    color={getRoleColor(user.role)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {user.role === UserRole.ADMIN ? (
                      <Chip label="全部工作区" color="primary" size="small" />
                    ) : user.workspaceIds.length > 0 ? (
                      user.workspaceIds.slice(0, 2).map(id => {
                        const workspace = workspaces.find(w => w.id === id);
                        return workspace ? (
                          <Chip key={id} label={workspace.name} size="small" />
                        ) : null;
                      })
                    ) : (
                      <Typography variant="caption" color="text.secondary">
                        未分配
                      </Typography>
                    )}
                    {user.workspaceIds.length > 2 && (
                      <Chip label={`+${user.workspaceIds.length - 2}`} size="small" variant="outlined" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={user.isActive ? '活跃' : '禁用'}
                    color={user.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="caption">
                    {user.lastLoginAt 
                      ? new Date(user.lastLoginAt).toLocaleString()
                      : '从未登录'
                    }
                  </Typography>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleEditUser(user)}
                  >
                    <Edit />
                  </IconButton>
                  {user.role === UserRole.OPERATOR && (
                    <IconButton
                      size="small"
                      onClick={() => handleManageWorkspaces(user)}
                    >
                      <Assignment />
                    </IconButton>
                  )}
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDeleteUser(user)}
                  >
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 用户编辑对话框 */}
      <Dialog open={userDialogOpen} onClose={() => setUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? '编辑用户' : '创建用户'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="用户名"
            fullWidth
            variant="outlined"
            value={userForm.username}
            onChange={(e) => setUserForm(prev => ({ ...prev, username: e.target.value }))}
            sx={{ mb: 2 }}
          />
          
          <TextField
            margin="dense"
            label="邮箱"
            type="email"
            fullWidth
            variant="outlined"
            value={userForm.email}
            onChange={(e) => setUserForm(prev => ({ ...prev, email: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>角色</InputLabel>
            <Select
              value={userForm.role}
              label="角色"
              onChange={(e) => setUserForm(prev => ({ ...prev, role: e.target.value as UserRole }))}
            >
              <MenuItem value={UserRole.ADMIN}>管理员</MenuItem>
              <MenuItem value={UserRole.OPERATOR}>操作员</MenuItem>
            </Select>
          </FormControl>

          <FormControlLabel
            control={
              <Switch
                checked={userForm.isActive}
                onChange={(e) => setUserForm(prev => ({ ...prev, isActive: e.target.checked }))}
              />
            }
            label="启用用户"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDialogOpen(false)}>取消</Button>
          <Button onClick={handleSaveUser} variant="contained" disabled={loading}>
            {editingUser ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 工作区分配对话框 */}
      <Dialog open={workspaceDialogOpen} onClose={() => setWorkspaceDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          工作区分配 - {selectedUser?.username}
        </DialogTitle>
        <DialogContent>
          {workspaces.length === 0 ? (
            <Alert severity="info">
              暂无工作区。请先在工作区管理页面创建工作区。
            </Alert>
          ) : (
            <Box>
              {workspaces.map((workspace) => (
                <FormControlLabel
                  key={workspace.id}
                  control={
                    <Switch
                      checked={selectedUser?.workspaceIds.includes(workspace.id) || false}
                      onChange={(e) => handleWorkspaceAssignment(workspace.id, e.target.checked)}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="subtitle2">{workspace.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {workspace.description}
                      </Typography>
                    </Box>
                  }
                  sx={{ display: 'block', mb: 1 }}
                />
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWorkspaceDialogOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default UserManagement;
