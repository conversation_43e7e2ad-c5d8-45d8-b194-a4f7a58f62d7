import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  PlayArrow as DeployIcon,
  Visibility as PreviewIcon,
  Save as SaveIcon,
  NetworkCheck as NetworkIcon,
  Storage as StorageIcon
} from '@mui/icons-material';

// 接口定义
interface Container {
  name: string;
  image: string;
  tag: string;
  ports: Port[];
  env: EnvVar[];
  resources: Resources;
}

interface Port {
  name: string;
  containerPort: number;
  protocol: 'TCP' | 'UDP';
}

interface EnvVar {
  name: string;
  value?: string;
}

interface Resources {
  requests: { cpu: string; memory: string };
  limits: { cpu: string; memory: string };
}

interface Service {
  name: string;
  type: 'ClusterIP' | 'NodePort' | 'LoadBalancer';
  ports: ServicePort[];
}

interface ServicePort {
  name: string;
  port: number;
  targetPort: number;
  nodePort?: number;
}

interface Volume {
  name: string;
  type: 'emptyDir' | 'configMap' | 'secret' | 'persistentVolumeClaim';
  source: string;
  mountPath: string;
}

const ApplicationDeployment: React.FC = () => {
  // 状态管理
  const [activeStep, setActiveStep] = useState(0);
  const [appName, setAppName] = useState('');
  const [namespace, setNamespace] = useState('default');
  const [replicas, setReplicas] = useState(1);
  const [containers, setContainers] = useState<Container[]>([{
    name: '',
    image: '',
    tag: 'latest',
    ports: [],
    env: [],
    resources: {
      requests: { cpu: '100m', memory: '128Mi' },
      limits: { cpu: '500m', memory: '512Mi' }
    }
  }]);
  const [service, setService] = useState<Service>({
    name: '',
    type: 'ClusterIP',
    ports: []
  });
  const [volumes, setVolumes] = useState<Volume[]>([]);
  const [deploymentStrategy, setDeploymentStrategy] = useState('RollingUpdate');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [deploying, setDeploying] = useState(false);

  const steps = [
    '基本信息',
    '容器配置', 
    '网络配置',
    '存储配置',
    '部署策略',
    '预览部署'
  ];

  // 工具函数
  const addContainer = () => {
    setContainers([...containers, {
      name: '',
      image: '',
      tag: 'latest',
      ports: [],
      env: [],
      resources: {
        requests: { cpu: '100m', memory: '128Mi' },
        limits: { cpu: '500m', memory: '512Mi' }
      }
    }]);
  };

  const removeContainer = (index: number) => {
    setContainers(containers.filter((_, i) => i !== index));
  };

  const updateContainer = (index: number, field: string, value: any) => {
    const updated = [...containers];
    updated[index] = { ...updated[index], [field]: value };
    setContainers(updated);
  };

  const addPort = (containerIndex: number) => {
    const updated = [...containers];
    updated[containerIndex].ports.push({
      name: '',
      containerPort: 8080,
      protocol: 'TCP'
    });
    setContainers(updated);
  };

  const addEnvVar = (containerIndex: number) => {
    const updated = [...containers];
    updated[containerIndex].env.push({
      name: '',
      value: ''
    });
    setContainers(updated);
  };

  const addVolume = () => {
    setVolumes([...volumes, {
      name: '',
      type: 'emptyDir',
      source: '',
      mountPath: ''
    }]);
  };

  const generateYAML = () => {
    const deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace
      },
      spec: {
        replicas: replicas,
        strategy: {
          type: deploymentStrategy
        },
        selector: {
          matchLabels: {
            app: appName
          }
        },
        template: {
          metadata: {
            labels: {
              app: appName
            }
          },
          spec: {
            containers: containers.map(container => ({
              name: container.name,
              image: `${container.image}:${container.tag}`,
              ports: container.ports.map(port => ({
                name: port.name,
                containerPort: port.containerPort,
                protocol: port.protocol
              })),
              env: container.env,
              resources: container.resources
            })),
            volumes: volumes.map(volume => ({
              name: volume.name,
              [volume.type]: volume.type === 'emptyDir' ? {} : { name: volume.source }
            }))
          }
        }
      }
    };

    return JSON.stringify(deployment, null, 2);
  };

  const handleDeploy = async () => {
    setDeploying(true);
    try {
      console.log('Deploying application:', generateYAML());
      await new Promise(resolve => setTimeout(resolve, 3000));
      alert('应用部署成功！');
    } catch (error) {
      console.error('Deployment failed:', error);
      alert('部署失败，请检查配置');
    } finally {
      setDeploying(false);
    }
  };

  // 渲染函数
  const renderBasicInfo = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          应用基本信息
        </Typography>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="应用名称"
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              required
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl fullWidth>
              <InputLabel>命名空间</InputLabel>
              <Select
                value={namespace}
                onChange={(e) => setNamespace(e.target.value)}
              >
                <MenuItem value="default">default</MenuItem>
                <MenuItem value="production">production</MenuItem>
                <MenuItem value="staging">staging</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="副本数量"
              type="number"
              value={replicas}
              onChange={(e) => setReplicas(parseInt(e.target.value))}
              inputProps={{ min: 1, max: 100 }}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderContainerConfig = () => (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            容器配置
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={addContainer}
            variant="outlined"
          >
            添加容器
          </Button>
        </Box>

        {containers.map((container, index) => (
          <Accordion key={index} defaultExpanded={index === 0}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>
                容器 {index + 1}: {container.name || '未命名'}
              </Typography>
              {containers.length > 1 && (
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    removeContainer(index);
                  }}
                  size="small"
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    label="容器名称"
                    value={container.name}
                    onChange={(e) => updateContainer(index, 'name', e.target.value)}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label="镜像地址"
                    value={container.image}
                    onChange={(e) => updateContainer(index, 'image', e.target.value)}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 2 }}>
                  <TextField
                    fullWidth
                    label="标签"
                    value={container.tag}
                    onChange={(e) => updateContainer(index, 'tag', e.target.value)}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        ))}
      </CardContent>
    </Card>
  );

  const renderSimplifiedView = () => (
    <Card>
      <CardContent>
        <Alert severity="info" sx={{ mb: 3 }}>
          🚀 完整的容器部署功能正在开发中，当前提供简化版本用于演示。
        </Alert>

        <Typography variant="h6" gutterBottom>
          快速部署
        </Typography>

        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="应用名称"
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              placeholder="例如: my-web-app"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="镜像地址"
              value={containers[0]?.image || ''}
              onChange={(e) => updateContainer(0, 'image', e.target.value)}
              placeholder="例如: nginx:latest"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              label="副本数量"
              type="number"
              value={replicas}
              onChange={(e) => setReplicas(parseInt(e.target.value))}
              inputProps={{ min: 1, max: 10 }}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <FormControl fullWidth>
              <InputLabel>命名空间</InputLabel>
              <Select
                value={namespace}
                onChange={(e) => setNamespace(e.target.value)}
              >
                <MenuItem value="default">default</MenuItem>
                <MenuItem value="production">production</MenuItem>
                <MenuItem value="staging">staging</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <FormControl fullWidth>
              <InputLabel>部署策略</InputLabel>
              <Select
                value={deploymentStrategy}
                onChange={(e) => setDeploymentStrategy(e.target.value)}
              >
                <MenuItem value="RollingUpdate">滚动更新</MenuItem>
                <MenuItem value="Recreate">重新创建</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={3}>
          <Typography variant="subtitle2" gutterBottom>
            部署预览
          </Typography>
          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Typography variant="body2" color="text.secondary">
              应用名称: {appName || '未设置'}<br/>
              镜像: {containers[0]?.image || '未设置'}<br/>
              副本数: {replicas}<br/>
              命名空间: {namespace}<br/>
              策略: {deploymentStrategy === 'RollingUpdate' ? '滚动更新' : '重新创建'}
            </Typography>
          </Paper>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="lg">
      <Box py={3}>
        <Typography variant="h4" gutterBottom>
          应用部署管理
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          🎯 KubeX 应用部署系统 - 简化Kubernetes应用部署流程
        </Alert>

        {deploying && (
          <Box mb={2}>
            <Alert severity="info">
              正在部署应用，请稍候...
            </Alert>
            <LinearProgress sx={{ mt: 1 }} />
          </Box>
        )}

        <Box mb={3}>
          {activeStep === 0 && renderBasicInfo()}
          {activeStep === 1 && renderContainerConfig()}
          {activeStep === 2 && renderSimplifiedView()}
        </Box>

        <Box display="flex" justifyContent="space-between">
          <Button
            disabled={activeStep === 0}
            onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
          >
            上一步
          </Button>

          <Box>
            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={() => setPreviewOpen(true)}
              sx={{ mr: 1 }}
              disabled={!appName}
            >
              预览YAML
            </Button>

            <Button
              variant="contained"
              startIcon={<DeployIcon />}
              onClick={handleDeploy}
              disabled={deploying || !appName || !containers[0]?.image}
            >
              部署应用
            </Button>
          </Box>
        </Box>

        {/* YAML预览对话框 */}
        <Dialog
          open={previewOpen}
          onClose={() => setPreviewOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>部署配置预览</DialogTitle>
          <DialogContent>
            <pre style={{
              background: '#f5f5f5',
              padding: '16px',
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px'
            }}>
              {generateYAML()}
            </pre>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>
              关闭
            </Button>
            <Button variant="contained" startIcon={<SaveIcon />}>
              保存配置
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default ApplicationDeployment;
