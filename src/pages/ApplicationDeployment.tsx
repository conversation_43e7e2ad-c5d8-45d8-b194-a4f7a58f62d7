import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  PlayArrow as DeployIcon,
  Visibility as PreviewIcon,
  Save as SaveIcon,
  CloudUpload as UploadIcon,
  Settings as SettingsIcon,
  Network as NetworkIcon,
  Storage as StorageIcon,
  Security as SecurityIcon,
  MonitorHeart as HealthIcon
} from '@mui/icons-material';

interface Container {
  name: string;
  image: string;
  tag: string;
  ports: Port[];
  env: EnvVar[];
  resources: Resources;
  healthChecks: HealthChecks;
}

interface Port {
  name: string;
  containerPort: number;
  protocol: 'TCP' | 'UDP';
}

interface EnvVar {
  name: string;
  value?: string;
  valueFrom?: {
    configMapKeyRef?: { name: string; key: string };
    secretKeyRef?: { name: string; key: string };
  };
}

interface Resources {
  requests: { cpu: string; memory: string };
  limits: { cpu: string; memory: string };
}

interface HealthChecks {
  livenessProbe?: Probe;
  readinessProbe?: Probe;
}

interface Probe {
  httpGet?: { path: string; port: number };
  exec?: { command: string[] };
  initialDelaySeconds: number;
  periodSeconds: number;
}

interface Service {
  name: string;
  type: 'ClusterIP' | 'NodePort' | 'LoadBalancer';
  ports: ServicePort[];
}

interface ServicePort {
  name: string;
  port: number;
  targetPort: number;
  nodePort?: number;
}

interface Volume {
  name: string;
  type: 'emptyDir' | 'configMap' | 'secret' | 'persistentVolumeClaim';
  source: string;
  mountPath: string;
}

const ApplicationDeployment: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [appName, setAppName] = useState('');
  const [namespace, setNamespace] = useState('default');
  const [replicas, setReplicas] = useState(1);
  const [containers, setContainers] = useState<Container[]>([{
    name: '',
    image: '',
    tag: 'latest',
    ports: [],
    env: [],
    resources: {
      requests: { cpu: '100m', memory: '128Mi' },
      limits: { cpu: '500m', memory: '512Mi' }
    },
    healthChecks: {}
  }]);
  const [service, setService] = useState<Service>({
    name: '',
    type: 'ClusterIP',
    ports: []
  });
  const [volumes, setVolumes] = useState<Volume[]>([]);
  const [deploymentStrategy, setDeploymentStrategy] = useState('RollingUpdate');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [deploying, setDeploying] = useState(false);

  const steps = [
    '基本信息',
    '容器配置',
    '网络配置',
    '存储配置',
    '部署策略',
    '预览部署'
  ];

  const addContainer = () => {
    setContainers([...containers, {
      name: '',
      image: '',
      tag: 'latest',
      ports: [],
      env: [],
      resources: {
        requests: { cpu: '100m', memory: '128Mi' },
        limits: { cpu: '500m', memory: '512Mi' }
      },
      healthChecks: {}
    }]);
  };

  const removeContainer = (index: number) => {
    setContainers(containers.filter((_, i) => i !== index));
  };

  const updateContainer = (index: number, field: string, value: any) => {
    const updated = [...containers];
    updated[index] = { ...updated[index], [field]: value };
    setContainers(updated);
  };

  const addPort = (containerIndex: number) => {
    const updated = [...containers];
    updated[containerIndex].ports.push({
      name: '',
      containerPort: 8080,
      protocol: 'TCP'
    });
    setContainers(updated);
  };

  const addEnvVar = (containerIndex: number) => {
    const updated = [...containers];
    updated[containerIndex].env.push({
      name: '',
      value: ''
    });
    setContainers(updated);
  };

  const addVolume = () => {
    setVolumes([...volumes, {
      name: '',
      type: 'emptyDir',
      source: '',
      mountPath: ''
    }]);
  };

  const generateYAML = () => {
    const deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace
      },
      spec: {
        replicas: replicas,
        strategy: {
          type: deploymentStrategy
        },
        selector: {
          matchLabels: {
            app: appName
          }
        },
        template: {
          metadata: {
            labels: {
              app: appName
            }
          },
          spec: {
            containers: containers.map(container => ({
              name: container.name,
              image: `${container.image}:${container.tag}`,
              ports: container.ports.map(port => ({
                name: port.name,
                containerPort: port.containerPort,
                protocol: port.protocol
              })),
              env: container.env,
              resources: container.resources,
              ...container.healthChecks
            })),
            volumes: volumes.map(volume => ({
              name: volume.name,
              [volume.type]: volume.type === 'emptyDir' ? {} : { name: volume.source }
            }))
          }
        }
      }
    };

    return JSON.stringify(deployment, null, 2);
  };

  const handleDeploy = async () => {
    setDeploying(true);
    try {
      // 这里调用实际的部署API
      console.log('Deploying application:', generateYAML());
      // 模拟部署过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      alert('应用部署成功！');
    } catch (error) {
      console.error('Deployment failed:', error);
      alert('部署失败，请检查配置');
    } finally {
      setDeploying(false);
    }
  };

  const renderBasicInfo = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          应用基本信息
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="应用名称"
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>命名空间</InputLabel>
              <Select
                value={namespace}
                onChange={(e) => setNamespace(e.target.value)}
              >
                <MenuItem value="default">default</MenuItem>
                <MenuItem value="production">production</MenuItem>
                <MenuItem value="staging">staging</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="副本数量"
              type="number"
              value={replicas}
              onChange={(e) => setReplicas(parseInt(e.target.value))}
              inputProps={{ min: 1, max: 100 }}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderContainerConfig = () => (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            容器配置
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={addContainer}
            variant="outlined"
          >
            添加容器
          </Button>
        </Box>

        {containers.map((container, index) => (
          <Accordion key={index} defaultExpanded={index === 0}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>
                容器 {index + 1}: {container.name || '未命名'}
              </Typography>
              {containers.length > 1 && (
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    removeContainer(index);
                  }}
                  size="small"
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="容器名称"
                    value={container.name}
                    onChange={(e) => updateContainer(index, 'name', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="镜像地址"
                    value={container.image}
                    onChange={(e) => updateContainer(index, 'image', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    label="标签"
                    value={container.tag}
                    onChange={(e) => updateContainer(index, 'tag', e.target.value)}
                  />
                </Grid>

                {/* 端口配置 */}
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mt={2} mb={1}>
                    <Typography variant="subtitle2">端口配置</Typography>
                    <Button size="small" onClick={() => addPort(index)}>
                      添加端口
                    </Button>
                  </Box>
                  {container.ports.map((port, portIndex) => (
                    <Grid container spacing={1} key={portIndex} sx={{ mb: 1 }}>
                      <Grid item xs={4}>
                        <TextField
                          size="small"
                          label="端口名称"
                          value={port.name}
                          onChange={(e) => {
                            const updated = [...containers];
                            updated[index].ports[portIndex].name = e.target.value;
                            setContainers(updated);
                          }}
                        />
                      </Grid>
                      <Grid item xs={3}>
                        <TextField
                          size="small"
                          label="端口号"
                          type="number"
                          value={port.containerPort}
                          onChange={(e) => {
                            const updated = [...containers];
                            updated[index].ports[portIndex].containerPort = parseInt(e.target.value);
                            setContainers(updated);
                          }}
                        />
                      </Grid>
                      <Grid item xs={3}>
                        <FormControl size="small" fullWidth>
                          <InputLabel>协议</InputLabel>
                          <Select
                            value={port.protocol}
                            onChange={(e) => {
                              const updated = [...containers];
                              updated[index].ports[portIndex].protocol = e.target.value as 'TCP' | 'UDP';
                              setContainers(updated);
                            }}
                          >
                            <MenuItem value="TCP">TCP</MenuItem>
                            <MenuItem value="UDP">UDP</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={2}>
                        <IconButton
                          size="small"
                          onClick={() => {
                            const updated = [...containers];
                            updated[index].ports.splice(portIndex, 1);
                            setContainers(updated);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>

                {/* 环境变量配置 */}
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mt={2} mb={1}>
                    <Typography variant="subtitle2">环境变量</Typography>
                    <Button size="small" onClick={() => addEnvVar(index)}>
                      添加环境变量
                    </Button>
                  </Box>
                  {container.env.map((env, envIndex) => (
                    <Grid container spacing={1} key={envIndex} sx={{ mb: 1 }}>
                      <Grid item xs={4}>
                        <TextField
                          size="small"
                          label="变量名"
                          value={env.name}
                          onChange={(e) => {
                            const updated = [...containers];
                            updated[index].env[envIndex].name = e.target.value;
                            setContainers(updated);
                          }}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <TextField
                          size="small"
                          label="变量值"
                          value={env.value || ''}
                          onChange={(e) => {
                            const updated = [...containers];
                            updated[index].env[envIndex].value = e.target.value;
                            setContainers(updated);
                          }}
                        />
                      </Grid>
                      <Grid item xs={2}>
                        <IconButton
                          size="small"
                          onClick={() => {
                            const updated = [...containers];
                            updated[index].env.splice(envIndex, 1);
                            setContainers(updated);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>

                {/* 资源限制 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>资源限制</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" gutterBottom>请求资源</Typography>
                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            label="CPU"
                            value={container.resources.requests.cpu}
                            onChange={(e) => {
                              const updated = [...containers];
                              updated[index].resources.requests.cpu = e.target.value;
                              setContainers(updated);
                            }}
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            label="内存"
                            value={container.resources.requests.memory}
                            onChange={(e) => {
                              const updated = [...containers];
                              updated[index].resources.requests.memory = e.target.value;
                              setContainers(updated);
                            }}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" gutterBottom>限制资源</Typography>
                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            label="CPU"
                            value={container.resources.limits.cpu}
                            onChange={(e) => {
                              const updated = [...containers];
                              updated[index].resources.limits.cpu = e.target.value;
                              setContainers(updated);
                            }}
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            label="内存"
                            value={container.resources.limits.memory}
                            onChange={(e) => {
                              const updated = [...containers];
                              updated[index].resources.limits.memory = e.target.value;
                              setContainers(updated);
                            }}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        ))}
      </CardContent>
    </Card>
  );

  const renderNetworkConfig = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom startIcon={<NetworkIcon />}>
          网络配置
        </Typography>

        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Service 配置</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Service 名称"
                  value={service.name}
                  onChange={(e) => setService({...service, name: e.target.value})}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Service 类型</InputLabel>
                  <Select
                    value={service.type}
                    onChange={(e) => setService({...service, type: e.target.value as any})}
                  >
                    <MenuItem value="ClusterIP">ClusterIP</MenuItem>
                    <MenuItem value="NodePort">NodePort</MenuItem>
                    <MenuItem value="LoadBalancer">LoadBalancer</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mt={2} mb={1}>
                  <Typography variant="subtitle2">端口映射</Typography>
                  <Button
                    size="small"
                    onClick={() => setService({
                      ...service,
                      ports: [...service.ports, {name: '', port: 80, targetPort: 8080}]
                    })}
                  >
                    添加端口
                  </Button>
                </Box>
                {service.ports.map((port, index) => (
                  <Grid container spacing={1} key={index} sx={{ mb: 1 }}>
                    <Grid item xs={3}>
                      <TextField
                        size="small"
                        label="端口名称"
                        value={port.name}
                        onChange={(e) => {
                          const updated = [...service.ports];
                          updated[index].name = e.target.value;
                          setService({...service, ports: updated});
                        }}
                      />
                    </Grid>
                    <Grid item xs={3}>
                      <TextField
                        size="small"
                        label="Service端口"
                        type="number"
                        value={port.port}
                        onChange={(e) => {
                          const updated = [...service.ports];
                          updated[index].port = parseInt(e.target.value);
                          setService({...service, ports: updated});
                        }}
                      />
                    </Grid>
                    <Grid item xs={3}>
                      <TextField
                        size="small"
                        label="目标端口"
                        type="number"
                        value={port.targetPort}
                        onChange={(e) => {
                          const updated = [...service.ports];
                          updated[index].targetPort = parseInt(e.target.value);
                          setService({...service, ports: updated});
                        }}
                      />
                    </Grid>
                    {service.type === 'NodePort' && (
                      <Grid item xs={2}>
                        <TextField
                          size="small"
                          label="NodePort"
                          type="number"
                          value={port.nodePort || ''}
                          onChange={(e) => {
                            const updated = [...service.ports];
                            updated[index].nodePort = parseInt(e.target.value);
                            setService({...service, ports: updated});
                          }}
                        />
                      </Grid>
                    )}
                    <Grid item xs={1}>
                      <IconButton
                        size="small"
                        onClick={() => {
                          const updated = service.ports.filter((_, i) => i !== index);
                          setService({...service, ports: updated});
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </CardContent>
    </Card>
  );

  const renderStorageConfig = () => (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" startIcon={<StorageIcon />}>
            存储配置
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={addVolume}
            variant="outlined"
          >
            添加存储卷
          </Button>
        </Box>

        {volumes.map((volume, index) => (
          <Accordion key={index}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>
                存储卷 {index + 1}: {volume.name || '未命名'}
              </Typography>
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  setVolumes(volumes.filter((_, i) => i !== index));
                }}
                size="small"
                color="error"
              >
                <DeleteIcon />
              </IconButton>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="存储卷名称"
                    value={volume.name}
                    onChange={(e) => {
                      const updated = [...volumes];
                      updated[index].name = e.target.value;
                      setVolumes(updated);
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>存储类型</InputLabel>
                    <Select
                      value={volume.type}
                      onChange={(e) => {
                        const updated = [...volumes];
                        updated[index].type = e.target.value as any;
                        setVolumes(updated);
                      }}
                    >
                      <MenuItem value="emptyDir">临时存储 (emptyDir)</MenuItem>
                      <MenuItem value="configMap">配置映射 (ConfigMap)</MenuItem>
                      <MenuItem value="secret">密钥 (Secret)</MenuItem>
                      <MenuItem value="persistentVolumeClaim">持久卷 (PVC)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                {volume.type !== 'emptyDir' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="源名称"
                      value={volume.source}
                      onChange={(e) => {
                        const updated = [...volumes];
                        updated[index].source = e.target.value;
                        setVolumes(updated);
                      }}
                    />
                  </Grid>
                )}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="挂载路径"
                    value={volume.mountPath}
                    onChange={(e) => {
                      const updated = [...volumes];
                      updated[index].mountPath = e.target.value;
                      setVolumes(updated);
                    }}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        ))}

        {volumes.length === 0 && (
          <Alert severity="info">
            暂无存储卷配置。点击"添加存储卷"来配置持久化存储。
          </Alert>
        )}
      </CardContent>
    </Card>
  );

  const renderDeploymentStrategy = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          部署策略
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>更新策略</InputLabel>
              <Select
                value={deploymentStrategy}
                onChange={(e) => setDeploymentStrategy(e.target.value)}
              >
                <MenuItem value="RollingUpdate">滚动更新</MenuItem>
                <MenuItem value="Recreate">重新创建</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {deploymentStrategy === 'RollingUpdate' && (
            <>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="最大不可用"
                  defaultValue="25%"
                  helperText="滚动更新时最大不可用Pod数"
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="最大超出"
                  defaultValue="25%"
                  helperText="滚动更新时最大超出Pod数"
                />
              </Grid>
            </>
          )}

          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              部署选项
            </Typography>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="启用就绪探针"
            />
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="启用存活探针"
            />
            <FormControlLabel
              control={<Switch />}
              label="启用自动扩缩容 (HPA)"
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderPreviewAndDeploy = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          部署预览
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  部署摘要
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell>应用名称</TableCell>
                        <TableCell>{appName}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>命名空间</TableCell>
                        <TableCell>{namespace}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>副本数量</TableCell>
                        <TableCell>{replicas}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>容器数量</TableCell>
                        <TableCell>{containers.length}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>存储卷</TableCell>
                        <TableCell>{volumes.length}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Service类型</TableCell>
                        <TableCell>{service.type}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  容器镜像
                </Typography>
                {containers.map((container, index) => (
                  <Chip
                    key={index}
                    label={`${container.name}: ${container.image}:${container.tag}`}
                    sx={{ mr: 1, mb: 1 }}
                  />
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Alert severity="warning" sx={{ mt: 2 }}>
          请仔细检查配置信息，确认无误后点击"部署应用"按钮开始部署。
        </Alert>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="lg">
      <Box py={3}>
        <Typography variant="h4" gutterBottom>
          应用部署管理
        </Typography>
        
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {deploying && (
          <Box mb={2}>
            <Alert severity="info">
              正在部署应用，请稍候...
            </Alert>
            <LinearProgress sx={{ mt: 1 }} />
          </Box>
        )}

        <Box mb={3}>
          {activeStep === 0 && renderBasicInfo()}
          {activeStep === 1 && renderContainerConfig()}
          {activeStep === 2 && renderNetworkConfig()}
          {activeStep === 3 && renderStorageConfig()}
          {activeStep === 4 && renderDeploymentStrategy()}
          {activeStep === 5 && renderPreviewAndDeploy()}
        </Box>

        <Box display="flex" justifyContent="space-between">
          <Button
            disabled={activeStep === 0}
            onClick={() => setActiveStep(activeStep - 1)}
          >
            上一步
          </Button>
          
          <Box>
            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={() => setPreviewOpen(true)}
              sx={{ mr: 1 }}
            >
              预览YAML
            </Button>
            
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                startIcon={<DeployIcon />}
                onClick={handleDeploy}
                disabled={deploying || !appName}
              >
                部署应用
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={() => setActiveStep(activeStep + 1)}
                disabled={!appName}
              >
                下一步
              </Button>
            )}
          </Box>
        </Box>

        {/* YAML预览对话框 */}
        <Dialog
          open={previewOpen}
          onClose={() => setPreviewOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>部署配置预览</DialogTitle>
          <DialogContent>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px'
            }}>
              {generateYAML()}
            </pre>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>
              关闭
            </Button>
            <Button variant="contained" startIcon={<SaveIcon />}>
              保存配置
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default ApplicationDeployment;
