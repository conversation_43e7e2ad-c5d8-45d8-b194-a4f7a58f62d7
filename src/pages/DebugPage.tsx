import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  Settings as SettingsIcon,
  BugReport as BugIcon,
  Api as ApiIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon
} from '@mui/icons-material';

const DebugPage: React.FC = () => {
  const [apiLogs, setApiLogs] = useState<string[]>([]);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [configTest, setConfigTest] = useState<any>(null);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [testUrl, setTestUrl] = useState('');
  const [testResponse, setTestResponse] = useState('');

  useEffect(() => {
    // 模拟API日志
    const logs = [
      'GET /v3/clusters - 200 OK',
      'GET /k8s/clusters/local/api/v1/pods - 200 OK',
      'GET /k8s/clusters/local/apis/apps/v1/deployments - 200 OK',
      'GET /k8s/clusters/local/api/v1/services - 200 OK'
    ];
    setApiLogs(logs);

    // 模拟测试结果
    const results = [
      { name: 'Rancher API连接', status: 'success', message: '连接正常' },
      { name: 'Kubernetes API', status: 'success', message: '可访问' },
      { name: '认证状态', status: 'success', message: '已认证' },
      { name: '权限检查', status: 'warning', message: '部分权限受限' }
    ];
    setTestResults(results);
  }, []);

  const handleTestConnection = async () => {
    try {
      const response = await fetch('/v3/clusters');
      setConfigTest({
        status: response.ok ? 'success' : 'error',
        statusCode: response.status,
        message: response.ok ? '连接成功' : '连接失败'
      });
    } catch (error) {
      setConfigTest({
        status: 'error',
        statusCode: 0,
        message: '网络错误'
      });
    }
  };

  const handleApiTest = async () => {
    if (!testUrl) return;
    
    try {
      const response = await fetch(testUrl);
      const data = await response.text();
      setTestResponse(`状态: ${response.status}\n响应: ${data.substring(0, 500)}...`);
    } catch (error) {
      setTestResponse(`错误: ${error.message}`);
    }
  };

  const clearLogs = () => {
    setApiLogs([]);
  };

  const refreshData = () => {
    window.location.reload();
  };

  return (
    <Container maxWidth="lg">
      <Box py={3}>
        <Typography variant="h4" gutterBottom startIcon={<BugIcon />}>
          调试工具
        </Typography>
        
        <Grid container spacing={3}>
          {/* 系统状态 */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" startIcon={<SettingsIcon />}>
                    系统状态
                  </Typography>
                  <Button
                    startIcon={<RefreshIcon />}
                    onClick={refreshData}
                    size="small"
                  >
                    刷新
                  </Button>
                </Box>
                
                {testResults.map((result, index) => (
                  <Box key={index} display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">{result.name}</Typography>
                    <Chip
                      label={result.message}
                      color={result.status === 'success' ? 'success' : result.status === 'warning' ? 'warning' : 'error'}
                      size="small"
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* 连接测试 */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom startIcon={<NetworkIcon />}>
                  连接测试
                </Typography>
                
                <Box mb={2}>
                  <Button
                    variant="contained"
                    onClick={handleTestConnection}
                    startIcon={<ApiIcon />}
                    fullWidth
                  >
                    测试Rancher连接
                  </Button>
                </Box>
                
                {configTest && (
                  <Alert severity={configTest.status === 'success' ? 'success' : 'error'}>
                    状态码: {configTest.statusCode} - {configTest.message}
                  </Alert>
                )}
                
                <Box mt={2}>
                  <Button
                    variant="outlined"
                    onClick={() => setTestDialogOpen(true)}
                    fullWidth
                  >
                    自定义API测试
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* API日志 */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    API调用日志
                  </Typography>
                  <Button
                    startIcon={<ClearIcon />}
                    onClick={clearLogs}
                    size="small"
                  >
                    清空日志
                  </Button>
                </Box>
                
                <Paper variant="outlined" sx={{ p: 2, maxHeight: 300, overflow: 'auto' }}>
                  {apiLogs.length > 0 ? (
                    apiLogs.map((log, index) => (
                      <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace', mb: 0.5 }}>
                        {new Date().toLocaleTimeString()} - {log}
                      </Typography>
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      暂无日志记录
                    </Typography>
                  )}
                </Paper>
              </CardContent>
            </Card>
          </Grid>

          {/* 本地存储信息 */}
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6" startIcon={<StorageIcon />}>
                  本地存储信息
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>键</TableCell>
                        <TableCell>值</TableCell>
                        <TableCell>操作</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.keys(localStorage).map((key) => (
                        <TableRow key={key}>
                          <TableCell>{key}</TableCell>
                          <TableCell sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                            {localStorage.getItem(key)?.substring(0, 100)}...
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => localStorage.removeItem(key)}
                              color="error"
                            >
                              <ClearIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          </Grid>

          {/* 环境信息 */}
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">
                  环境信息
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2">浏览器信息</Typography>
                    <Typography variant="body2">User Agent: {navigator.userAgent}</Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2">屏幕信息</Typography>
                    <Typography variant="body2">
                      分辨率: {window.screen.width} x {window.screen.height}
                    </Typography>
                    <Typography variant="body2">
                      视口: {window.innerWidth} x {window.innerHeight}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2">网络信息</Typography>
                    <Typography variant="body2">
                      在线状态: {navigator.onLine ? '在线' : '离线'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2">时间信息</Typography>
                    <Typography variant="body2">
                      当前时间: {new Date().toLocaleString()}
                    </Typography>
                    <Typography variant="body2">
                      时区: {Intl.DateTimeFormat().resolvedOptions().timeZone}
                    </Typography>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>

        {/* API测试对话框 */}
        <Dialog
          open={testDialogOpen}
          onClose={() => setTestDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>自定义API测试</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="API URL"
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="/v3/clusters"
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              onClick={handleApiTest}
              disabled={!testUrl}
              sx={{ mb: 2 }}
            >
              发送请求
            </Button>
            {testResponse && (
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>响应结果:</Typography>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {testResponse}
                </pre>
              </Paper>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setTestDialogOpen(false)}>
              关闭
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default DebugPage;
