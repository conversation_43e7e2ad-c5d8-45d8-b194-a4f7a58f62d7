import React, { useState } from 'react';
import { 
  Container, 
  Typography, 
  TextField, 
  Button, 
  Box, 
  Paper, 
  Alert,
  CircularProgress 
} from '@mui/material';

const DebugPage: React.FC = () => {
  const [baseUrl, setBaseUrl] = useState('https://rancher.localhost');
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testBasicConnection = async () => {
    setLoading(true);
    addResult('开始测试基本连接...');

    try {
      // 在开发环境中使用代理路径
      const testUrl = import.meta.env.DEV ? '/rancher-api/' : baseUrl;
      addResult(`测试 URL: ${testUrl}`);

      const response = await fetch(testUrl, {
        method: 'GET',
        mode: 'cors',
      });
      addResult(`基本连接测试 - 状态: ${response.status}`);
      addResult(`响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
    } catch (error) {
      addResult(`基本连接失败: ${error.message}`);
    }

    setLoading(false);
  };

  const testApiEndpoint = async () => {
    setLoading(true);
    addResult('开始测试 API 端点...');

    try {
      // 在开发环境中使用代理路径
      const url = import.meta.env.DEV ? '/rancher-api/v3/clusters' : `${baseUrl}/v3/clusters`;
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      addResult(`请求 URL: ${url}`);
      addResult(`请求头: ${JSON.stringify(headers)}`);

      const response = await fetch(url, {
        method: 'GET',
        headers,
        mode: 'cors',
      });

      addResult(`API 响应状态: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        addResult(`API 响应成功: ${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        addResult(`API 响应错误: ${errorText}`);
      }
    } catch (error) {
      addResult(`API 请求失败: ${error.message}`);
      addResult(`错误详情: ${JSON.stringify({
        name: error.name,
        message: error.message,
        stack: error.stack
      })}`);
    }

    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        Rancher 连接调试工具
      </Typography>
      
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Rancher Base URL"
          value={baseUrl}
          onChange={(e) => setBaseUrl(e.target.value)}
          sx={{ mb: 2 }}
        />
        <TextField
          fullWidth
          label="Bearer Token"
          type="password"
          value={token}
          onChange={(e) => setToken(e.target.value)}
          sx={{ mb: 2 }}
        />
      </Box>

      <Box sx={{ mb: 3 }}>
        <Button 
          variant="contained" 
          onClick={testBasicConnection}
          disabled={loading}
          sx={{ mr: 2 }}
        >
          测试基本连接
        </Button>
        <Button 
          variant="contained" 
          onClick={testApiEndpoint}
          disabled={loading || !token}
          sx={{ mr: 2 }}
        >
          测试 API 端点
        </Button>
        <Button 
          variant="outlined" 
          onClick={clearResults}
          disabled={loading}
        >
          清除结果
        </Button>
      </Box>

      {loading && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography>测试中...</Typography>
        </Box>
      )}

      <Paper sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
        <Typography variant="h6" gutterBottom>
          测试结果:
        </Typography>
        {results.length === 0 ? (
          <Typography color="text.secondary">
            点击上方按钮开始测试
          </Typography>
        ) : (
          results.map((result, index) => (
            <Typography 
              key={index} 
              variant="body2" 
              sx={{ 
                fontFamily: 'monospace', 
                mb: 1,
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-all'
              }}
            >
              {result}
            </Typography>
          ))
        )}
      </Paper>

      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>使用说明:</strong><br/>
          1. 首先测试基本连接，确保能够访问 Rancher 服务器<br/>
          2. 然后输入您的 Bearer Token 并测试 API 端点<br/>
          3. 查看详细的错误信息来诊断问题<br/>
          4. 如果遇到 CORS 错误，可能需要配置 Rancher 的 CORS 设置
        </Typography>
      </Alert>
    </Container>
  );
};

export default DebugPage;
