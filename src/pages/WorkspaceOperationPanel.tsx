import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Apps,
  Add,
  PlayArrow,
  Stop,
  Refresh,
  Visibility,
  Edit,
  Delete,
  CloudUpload,
  Storage,
  Settings,
  Timeline,
  Description,
  Security,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Info
} from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import { simpleAuthService } from '../services/simpleAuth';
import ConfigDialog from '../components/ConfigDialog';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`workspace-tabpanel-${index}`}
      aria-labelledby={`workspace-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const WorkspaceOperationPanel: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedWorkspace, setSelectedWorkspace] = useState('');
  const [workspaces, setWorkspaces] = useState([]);
  const [applications, setApplications] = useState([]);
  const [services, setServices] = useState([]);
  const [pods, setPods] = useState([]);
  const [configMaps, setConfigMaps] = useState([]);
  const [secrets, setSecrets] = useState([]);
  const [deployDialogOpen, setDeployDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [rancherApi, setRancherApi] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('未连接');
  const [connectionLatency, setConnectionLatency] = useState(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedApp, setSelectedApp] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const currentUser = simpleAuthService.getCurrentUser();

  // 部署表单状态
  const [deployForm, setDeployForm] = useState({
    name: '',
    image: '',
    namespace: '',
    replicas: 1,
    port: 80,
    env: []
  });

  useEffect(() => {
    initializeWorkspace();
  }, []);

  // 自动刷新功能
  useEffect(() => {
    if (autoRefresh && connectionStatus === '已连接') {
      const interval = setInterval(() => {
        console.log('Auto-refreshing data...');
        handleRefreshData();
      }, 30000); // 每30秒刷新一次

      setRefreshInterval(interval);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [autoRefresh, connectionStatus]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  const initializeWorkspace = async () => {
    setLoading(true);
    
    // 加载 Rancher 配置
    const savedConfig = localStorage.getItem('rancherConfig');
    if (savedConfig) {
      const config = JSON.parse(savedConfig);
      const api = new RancherAPI(config);
      setRancherApi(api);
      
      try {
        const status = await api.getConnectionStatus();
        if (status.connected) {
          setConnectionStatus('已连接');
          setConnectionLatency(status.latency);
          await loadUserWorkspaces(api);
        } else {
          setConnectionStatus('连接失败');
          setError(`连接失败: ${status.error}`);
          loadLocalWorkspaces();
        }
      } catch (error) {
        setConnectionStatus('连接失败');
        console.error('Rancher connection failed:', error);
        setError(`连接错误: ${error.message}`);
        loadLocalWorkspaces();
      }
    } else {
      setConnectionStatus('未配置');
      loadLocalWorkspaces();
    }
    
    setLoading(false);
  };

  const loadUserWorkspaces = async (api) => {
    try {
      // 加载用户有权限的工作区
      const workspaceConfigs = JSON.parse(localStorage.getItem('workspace_configs') || '[]');
      console.log('All workspace configs:', workspaceConfigs);
      console.log('Current user:', currentUser);

      let accessibleWorkspaces = workspaceConfigs;
      if (currentUser?.role !== 'admin') {
        accessibleWorkspaces = workspaceConfigs.filter(ws =>
          ws.assignedUsers?.includes(currentUser?.id)
        );
        console.log('Filtered workspaces for operator:', accessibleWorkspaces);
      } else {
        console.log('Admin user - showing all workspaces');
      }

      setWorkspaces(accessibleWorkspaces);
      if (accessibleWorkspaces.length > 0) {
        setSelectedWorkspace(accessibleWorkspaces[0].id);
        await loadWorkspaceData(api, accessibleWorkspaces[0]);
      } else {
        console.log('No accessible workspaces found');
        // 如果没有工作区配置，创建一个默认的演示工作区
        if (workspaceConfigs.length === 0) {
          const defaultWorkspace = {
            id: 'demo-workspace',
            name: '演示工作区',
            description: '默认演示工作区',
            namespaces: ['default', 'kube-system'],
            assignedUsers: currentUser?.role === 'admin' ? [] : [currentUser?.id],
            clusterId: 'local',
            created: new Date().toISOString(),
            source: 'local'
          };

          const newWorkspaces = [defaultWorkspace];
          localStorage.setItem('workspace_configs', JSON.stringify(newWorkspaces));
          setWorkspaces(newWorkspaces);
          setSelectedWorkspace(defaultWorkspace.id);
          await loadWorkspaceData(api, defaultWorkspace);
        }
      }
    } catch (error) {
      console.error('Failed to load user workspaces:', error);
      setError('加载工作区失败: ' + error.message);
    }
  };

  const loadLocalWorkspaces = () => {
    // 检查是否有工作区配置
    const savedWorkspaces = JSON.parse(localStorage.getItem('workspace_configs') || '[]');

    if (savedWorkspaces.length === 0) {
      // 创建默认工作区
      const defaultWorkspaces = [
        {
          id: 'ws-production',
          name: '生产环境',
          description: '生产环境工作区',
          namespaces: ['default', 'production'],
          assignedUsers: currentUser?.role === 'admin' ? [] : [currentUser?.id],
          clusterId: 'local',
          created: new Date().toISOString(),
          source: 'local'
        },
        {
          id: 'ws-development',
          name: '开发环境',
          description: '开发测试环境工作区',
          namespaces: ['development', 'testing'],
          assignedUsers: currentUser?.role === 'admin' ? [] : [currentUser?.id],
          clusterId: 'local',
          created: new Date().toISOString(),
          source: 'local'
        }
      ];

      localStorage.setItem('workspace_configs', JSON.stringify(defaultWorkspaces));
      setWorkspaces(defaultWorkspaces);
      setSelectedWorkspace(defaultWorkspaces[0].id);
    } else {
      // 根据用户权限过滤工作区
      let accessibleWorkspaces = savedWorkspaces;
      if (currentUser?.role !== 'admin') {
        accessibleWorkspaces = savedWorkspaces.filter(ws =>
          ws.assignedUsers?.includes(currentUser?.id)
        );
      }

      setWorkspaces(accessibleWorkspaces);
      if (accessibleWorkspaces.length > 0) {
        setSelectedWorkspace(accessibleWorkspaces[0].id);
      }
    }

    loadMockData();
  };

  const loadWorkspaceData = async (api, workspace) => {
    try {
      console.log('Loading workspace data for:', workspace.name);
      console.log('Cluster ID:', workspace.clusterId);
      console.log('Allowed namespaces:', workspace.namespaces);

      // 加载应用列表
      const deployments = await api.getDeployments(workspace.clusterId);
      console.log('Raw deployments from API:', deployments);

      const filteredApps = deployments
        .filter(app => workspace.namespaces.includes(app.namespace))
        .map(app => {
          console.log('Processing deployment:', app.name, {
            replicas: app.replicas,
            availableReplicas: app.availableReplicas,
            state: app.state,
            namespace: app.namespace
          });

          return {
            id: app.id,
            name: app.name,
            namespace: app.namespace,
            status: getAppStatus(app),
            replicas: `${app.availableReplicas || 0}/${app.replicas || 0}`,
            image: app.image || 'unknown',
            created: app.created ? new Date(app.created).toLocaleDateString() : '未知'
          };
        });

      console.log('Filtered applications:', filteredApps);
      setApplications(filteredApps);

      // 加载服务列表
      try {
        const servicesData = await api.getServices(workspace.clusterId);
        console.log('Raw services from API:', servicesData);
        const filteredServices = servicesData
          .filter(svc => workspace.namespaces.includes(svc.namespace));
        console.log('Filtered services:', filteredServices);
        setServices(filteredServices);
      } catch (serviceError) {
        console.error('Failed to load services:', serviceError);
        setServices([]);
      }

      // 加载 Pod 列表
      try {
        const podsData = await api.getPods(workspace.clusterId);
        console.log('Raw pods from API:', podsData);
        const filteredPods = podsData
          .filter(pod => workspace.namespaces.includes(pod.namespace));
        console.log('Filtered pods:', filteredPods);
        setPods(filteredPods);
      } catch (podError) {
        console.error('Failed to load pods:', podError);
        setPods([]);
      }

      // 加载 ConfigMaps
      try {
        const configMapsData = await api.getConfigMaps(workspace.clusterId);
        console.log('Raw configmaps from API:', configMapsData);
        const filteredConfigMaps = configMapsData
          .filter(cm => workspace.namespaces.includes(cm.namespace));
        console.log('Filtered configmaps:', filteredConfigMaps);
        setConfigMaps(filteredConfigMaps);
      } catch (configMapError) {
        console.error('Failed to load configmaps:', configMapError);
        setConfigMaps([]);
      }

      // 加载 Secrets
      try {
        const secretsData = await api.getSecrets(workspace.clusterId);
        console.log('Raw secrets from API:', secretsData);
        const filteredSecrets = secretsData
          .filter(secret => workspace.namespaces.includes(secret.namespace));
        console.log('Filtered secrets:', filteredSecrets);
        setSecrets(filteredSecrets);
      } catch (secretError) {
        console.error('Failed to load secrets:', secretError);
        setSecrets([]);
      }

    } catch (error) {
      console.error('Failed to load workspace data:', error);
      setError(`加载工作区数据失败: ${error.message}`);
      loadMockData();
    }
  };

  const loadMockData = () => {
    // 模拟数据
    setApplications([
      {
        id: 1,
        name: 'web-app',
        namespace: 'default',
        status: 'running',
        replicas: '3/3',
        image: 'nginx:1.21',
        created: '2024-01-15'
      },
      {
        id: 2,
        name: 'api-service',
        namespace: 'default',
        status: 'running',
        replicas: '2/2',
        image: 'node:16',
        created: '2024-01-14'
      }
    ]);

    setServices([
      { id: 1, name: 'web-service', namespace: 'default', type: 'ClusterIP', ports: '80/TCP' },
      { id: 2, name: 'api-service', namespace: 'default', type: 'LoadBalancer', ports: '3000/TCP' }
    ]);

    setPods([
      { id: 1, name: 'web-app-7d4b8c8f-abc12', namespace: 'default', status: 'Running', node: 'node-1' },
      { id: 2, name: 'web-app-7d4b8c8f-def34', namespace: 'default', status: 'Running', node: 'node-2' },
      { id: 3, name: 'api-service-5f6g7h8i-ghi56', namespace: 'default', status: 'Running', node: 'node-1' }
    ]);
  };

  const handleWorkspaceChange = async (workspaceId) => {
    setSelectedWorkspace(workspaceId);
    const workspace = workspaces.find(w => w.id === workspaceId);
    if (workspace && rancherApi) {
      await loadWorkspaceData(rancherApi, workspace);
    }
  };

  const handleDeployApplication = () => {
    const workspace = workspaces.find(w => w.id === selectedWorkspace);
    setDeployForm({
      name: '',
      image: '',
      namespace: workspace?.namespaces[0] || 'default',
      replicas: 1,
      port: 80,
      env: []
    });
    setDeployDialogOpen(true);
  };

  const handleSaveDeploy = async () => {
    if (!deployForm.name.trim() || !deployForm.image.trim()) {
      setError('应用名称和镜像不能为空');
      return;
    }

    setLoading(true);
    try {
      const workspace = workspaces.find(w => w.id === selectedWorkspace);

      if (rancherApi && workspace && connectionStatus === '已连接') {
        // 真实部署到 Rancher
        console.log('Deploying to Rancher:', deployForm);

        const appConfig = {
          name: deployForm.name,
          image: deployForm.image,
          replicas: deployForm.replicas,
          ports: deployForm.port ? [{ containerPort: deployForm.port }] : [],
          env: deployForm.env || [],
          labels: {
            'managed-by': 'kubex',
            'workspace': workspace.id
          }
        };

        const success = await rancherApi.deployApplication(
          workspace.clusterId,
          deployForm.namespace,
          appConfig
        );

        if (success) {
          setError(null);
          setDeployDialogOpen(false);

          // 刷新应用列表
          setTimeout(async () => {
            await loadWorkspaceData(rancherApi, workspace);
          }, 2000);

          console.log('Application deployed successfully');
        } else {
          throw new Error('部署请求失败');
        }
      } else {
        // 模拟部署（离线模式）
        console.log('Simulating deployment:', deployForm);

        const newApp = {
          id: Date.now(),
          name: deployForm.name,
          namespace: deployForm.namespace,
          status: 'pending',
          replicas: `0/${deployForm.replicas}`,
          image: deployForm.image,
          created: new Date().toISOString().split('T')[0]
        };

        setApplications(prev => [...prev, newApp]);
        setDeployDialogOpen(false);
        setError(null);

        // 模拟部署成功后状态更新
        setTimeout(() => {
          setApplications(prev => prev.map(app =>
            app.id === newApp.id
              ? { ...app, status: 'running', replicas: `${deployForm.replicas}/${deployForm.replicas}` }
              : app
          ));
        }, 3000);
      }

    } catch (error) {
      console.error('Deployment failed:', error);
      setError('部署失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleConfigSave = (config) => {
    localStorage.setItem('rancherConfig', JSON.stringify(config));
    setConfigDialogOpen(false);
    setError(null);
    // 清除缓存并重新初始化连接
    if (rancherApi) {
      rancherApi.clearCache();
    }
    initializeWorkspace();
  };

  const handleRefreshData = async () => {
    setLoading(true);
    setError(null);

    try {
      // 清除缓存
      if (rancherApi) {
        rancherApi.clearCache();
        console.log('Cache cleared, refreshing data...');
      }

      // 重新加载数据
      await initializeWorkspace();
      setLastRefresh(new Date());
    } catch (error) {
      setError(`刷新失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'default';
    switch (status.toLowerCase()) {
      case 'running': return 'success';
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'stopped': return 'warning';
      case 'inactive': return 'warning';
      case 'failed': return 'error';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    if (!status) return '未知';
    switch (status.toLowerCase()) {
      case 'running': return '运行中';
      case 'active': return '运行中';
      case 'pending': return '等待中';
      case 'stopped': return '已停止';
      case 'inactive': return '未激活';
      case 'failed': return '失败';
      case 'error': return '错误';
      case 'succeeded': return '已完成';
      case 'unknown': return '未知';
      default: return status;
    }
  };

  // Pod 状态颜色映射
  const getPodStatusColor = (status) => {
    if (!status) return 'default';
    switch (status.toLowerCase()) {
      case 'running': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      case 'unknown': return 'default';
      case 'succeeded': return 'info';
      default: return 'default';
    }
  };

  // 根据应用数据判断真实状态
  const getAppStatus = (app) => {
    // 如果副本数为 0，则认为是停止状态
    if (app.replicas === 0 || app.availableReplicas === 0) {
      return 'stopped';
    }

    // 如果可用副本数等于期望副本数，则认为是运行状态
    if (app.availableReplicas > 0 && app.availableReplicas === app.replicas) {
      return 'running';
    }

    // 如果有可用副本但不等于期望副本数，则认为是等待状态
    if (app.availableReplicas > 0 && app.availableReplicas < app.replicas) {
      return 'pending';
    }

    // 根据 Kubernetes 状态判断
    if (app.state === 'Active') {
      return 'running';
    } else if (app.state === 'Inactive' || app.state === 'Stopped') {
      return 'stopped';
    } else {
      return 'pending';
    }
  };

  // 判断应用是否正在运行（用于按钮显示）
  const isAppRunning = (app) => {
    // 检查副本数字符串格式 "1/1" 或 "0/1"
    if (app.replicas && typeof app.replicas === 'string' && app.replicas.includes('/')) {
      const [available, desired] = app.replicas.split('/').map(n => parseInt(n));
      console.log(`App ${app.name} replicas: available=${available}, desired=${desired}`);
      return desired > 0 && available > 0;
    }

    // 检查原始数据
    if (app.availableReplicas !== undefined && app.replicas !== undefined) {
      console.log(`App ${app.name} raw replicas: available=${app.availableReplicas}, desired=${app.replicas}`);
      return app.replicas > 0 && app.availableReplicas > 0;
    }

    // 根据状态判断
    const status = app.status || getAppStatus(app);
    console.log(`App ${app.name} status: ${status}`);
    return status === 'running' || status === '运行中';
  };

  // 查看应用详情
  const handleViewDetails = (app) => {
    try {
      console.log('Opening detail dialog for app:', app);
      setSelectedApp(app);
      setDetailDialogOpen(true);
    } catch (error) {
      console.error('Error opening detail dialog:', error);
      setError('打开详情对话框失败');
    }
  };

  // 编辑应用
  const handleEditApp = (app) => {
    try {
      console.log('Opening edit dialog for app:', app);
      setSelectedApp(app);
      setEditDialogOpen(true);
    } catch (error) {
      console.error('Error opening edit dialog:', error);
      setError('打开编辑对话框失败');
    }
  };

  // 应用操作功能
  const handleAppAction = async (action, app) => {
    console.log(`=== 开始执行 ${action} 操作 ===`);
    console.log('App:', app);
    console.log('Rancher API:', rancherApi);
    console.log('Connection Status:', connectionStatus);

    if (!rancherApi) {
      setError('Rancher API 未初始化');
      return;
    }

    if (connectionStatus !== '已连接') {
      setError('需要连接到 Rancher 才能执行此操作');
      return;
    }

    setLoading(true);

    try {
      const workspace = workspaces.find(w => w.id === selectedWorkspace);
      if (!workspace) {
        throw new Error('未找到工作区配置');
      }

      console.log('Workspace:', workspace);
      console.log(`Calling ${action} on cluster: ${workspace.clusterId}, namespace: ${app.namespace}, name: ${app.name}`);

      let success = false;

      switch (action) {
        case 'restart':
          console.log('调用 restartDeployment...');
          success = await rancherApi.restartDeployment(workspace.clusterId, app.namespace, app.name);
          console.log('Restart result:', success);
          break;
        case 'stop':
          console.log('调用 stopDeployment...');
          success = await rancherApi.stopDeployment(workspace.clusterId, app.namespace, app.name);
          console.log('Stop result:', success);
          break;
        case 'start':
          console.log('调用 startDeployment...');
          success = await rancherApi.startDeployment(workspace.clusterId, app.namespace, app.name);
          console.log('Start result:', success);
          break;
        case 'delete':
          if (window.confirm(`确定要删除应用 "${app.name}" 吗？此操作不可撤销。`)) {
            console.log('调用 deleteDeployment...');
            success = await rancherApi.deleteDeployment(workspace.clusterId, app.namespace, app.name);
            console.log('Delete result:', success);
          } else {
            setLoading(false);
            return;
          }
          break;
        default:
          throw new Error(`未知操作: ${action}`);
      }

      if (success) {
        setError(null);
        console.log(`✅ ${action} 操作成功`);

        // 清理缓存确保获取最新数据
        if (rancherApi.clearCache) {
          rancherApi.clearCache();
        }

        // 立即刷新一次
        console.log('立即刷新应用列表...');
        await loadWorkspaceData(rancherApi, workspace);

        // 快速再次刷新，确保状态更新
        setTimeout(async () => {
          console.log('快速刷新应用列表...');
          await loadWorkspaceData(rancherApi, workspace);
        }, 500);
      } else {
        throw new Error(`${action} 操作返回失败`);
      }
    } catch (error) {
      console.error(`❌ ${action} 操作失败:`, error);
      setError(`${action} 失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const currentWorkspace = workspaces.find(w => w.id === selectedWorkspace);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* 页面标题和工作区选择 */}
      <Box sx={{ 
        mb: 4, 
        p: 3, 
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
        borderRadius: 2,
        color: 'white'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
              <Apps sx={{ mr: 2, verticalAlign: 'middle', fontSize: 40 }} />
              工作区操作台
            </Typography>
            <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.9)' }}>
              {currentUser?.username} - {currentUser?.role === 'admin' ? '管理员' : '操作员'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              label={`连接: ${connectionStatus}${connectionLatency ? ` (${connectionLatency}ms)` : ''}`}
              color={connectionStatus === '已连接' ? 'success' : 'error'}
              sx={{ fontWeight: 'bold' }}
            />
            {lastRefresh && (
              <Chip
                label={`最后刷新: ${lastRefresh.toLocaleTimeString()}`}
                size="small"
                variant="outlined"
              />
            )}
            {connectionStatus !== '已连接' && (
              <Button
                variant="outlined"
                startIcon={<Settings />}
                onClick={() => setConfigDialogOpen(true)}
                sx={{
                  color: 'white',
                  borderColor: 'rgba(255,255,255,0.5)',
                  '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
                }}
              >
                配置连接
              </Button>
            )}
            <FormControlLabel
              control={
                <Switch
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  sx={{
                    '& .MuiSwitch-switchBase.Mui-checked': { color: 'white' },
                    '& .MuiSwitch-track': { backgroundColor: 'rgba(255,255,255,0.3)' }
                  }}
                />
              }
              label="自动刷新"
              sx={{ color: 'white', mr: 2 }}
            />
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefreshData}
              disabled={loading}
              sx={{
                color: 'white',
                borderColor: 'rgba(255,255,255,0.5)',
                '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' },
                mr: 1
              }}
            >
              {loading ? '刷新中...' : '刷新'}
            </Button>
            <Button
              variant="outlined"
              onClick={() => {
                console.log('=== 调试信息 ===');
                console.log('当前工作区:', workspaces);
                console.log('选中工作区:', selectedWorkspace);
                console.log('应用列表:', applications);
                console.log('服务列表:', services);
                console.log('Pod列表:', pods);
                console.log('连接状态:', connectionStatus);
                console.log('Rancher API:', rancherApi);
              }}
              sx={{
                color: 'white',
                borderColor: 'rgba(255,255,255,0.5)',
                '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
              }}
            >
              调试
            </Button>
          </Box>
        </Box>
        
        {/* 工作区选择 */}
        {workspaces.length > 0 && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6" sx={{ color: 'white' }}>
              当前工作区:
            </Typography>
            <FormControl sx={{ minWidth: 300 }}>
              <Select
                value={selectedWorkspace}
                onChange={(e) => handleWorkspaceChange(e.target.value)}
                sx={{ 
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  color: 'white',
                  '& .MuiOutlinedInput-notchedOutline': { borderColor: 'rgba(255,255,255,0.3)' },
                  '& .MuiSvgIcon-root': { color: 'white' }
                }}
              >
                {workspaces.map((workspace) => (
                  <MenuItem key={workspace.id} value={workspace.id}>
                    {workspace.name} ({workspace.namespaces?.length || 0} 个命名空间)
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )}
      </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 工作区为空提示 */}
      {workspaces.length === 0 && !loading && (
        <Alert severity="info" sx={{ mb: 3 }}>
          您还没有被分配到任何工作区。请联系管理员分配工作区权限。
        </Alert>
      )}

      {/* 主要内容区域 */}
      {currentWorkspace && (
        <Box>
          {/* 功能标签页 */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
              <Tab label="应用管理" icon={<Apps />} />
              <Tab label="服务管理" icon={<Storage />} />
              <Tab label="Pod 监控" icon={<Timeline />} />
              <Tab label="配置管理" icon={<Settings />} />
            </Tabs>
          </Box>

          {/* 应用管理标签页 */}
          <TabPanel value={currentTab} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5">应用部署管理</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    if (applications.length > 0) {
                      console.log('Testing detail dialog with first app:', applications[0]);
                      handleViewDetails(applications[0]);
                    } else {
                      console.log('No applications to test with');
                      setError('没有应用可以测试详情功能');
                    }
                  }}
                >
                  测试详情
                </Button>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleDeployApplication}
                >
                  部署应用
                </Button>
              </Box>
            </Box>
            
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>应用名称</TableCell>
                    <TableCell>命名空间</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>副本数</TableCell>
                    <TableCell>镜像</TableCell>
                    <TableCell>创建时间</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {applications.map((app) => (
                    <TableRow key={app.id} hover>
                      <TableCell>{app.name}</TableCell>
                      <TableCell>
                        <Chip label={app.namespace} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusText(app.status)}
                          color={getStatusColor(app.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{app.replicas}</TableCell>
                      <TableCell>
                        <Typography variant="caption">{app.image}</Typography>
                      </TableCell>
                      <TableCell>{app.created}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="查看详情">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleViewDetails(app)}
                            >
                              <Visibility />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="编辑">
                            <IconButton
                              size="small"
                              color="secondary"
                              onClick={() => handleEditApp(app)}
                            >
                              <Edit />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="重启">
                            <span>
                              <IconButton
                                size="small"
                                color="warning"
                                onClick={() => handleAppAction('restart', app)}
                                disabled={loading}
                              >
                                <Refresh />
                              </IconButton>
                            </span>
                          </Tooltip>
                          {isAppRunning(app) ? (
                            <Tooltip title="停止">
                              <span>
                                <IconButton
                                  size="small"
                                  color="warning"
                                  onClick={() => {
                                    console.log('Stopping app:', app);
                                    handleAppAction('stop', app);
                                  }}
                                  disabled={loading}
                                >
                                  <Stop />
                                </IconButton>
                              </span>
                            </Tooltip>
                          ) : (
                            <Tooltip title="启动">
                              <span>
                                <IconButton
                                  size="small"
                                  color="success"
                                  onClick={() => {
                                    console.log('Starting app:', app);
                                    handleAppAction('start', app);
                                  }}
                                  disabled={loading}
                                >
                                  <PlayArrow />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                          <Tooltip title={`当前状态: ${app.status}`}>
                            <IconButton
                              size="small"
                              color="info"
                              onClick={() => {
                                console.log('App details:', app);
                                console.log('App status:', app.status);
                                console.log('App replicas:', app.replicas);
                              }}
                            >
                              <Info />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="删除">
                            <span>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleAppAction('delete', app)}
                                disabled={loading}
                              >
                                <Delete />
                              </IconButton>
                            </span>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          {/* 服务管理标签页 */}
          <TabPanel value={currentTab} index={1}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h5" gutterBottom>服务管理</Typography>

              <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  服务数量: {services ? services.length : 0}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      console.log('=== 服务管理调试信息 ===');
                      console.log('Services:', services);
                      console.log('Services length:', services?.length);
                      console.log('Current tab:', currentTab);
                      console.log('Selected workspace:', selectedWorkspace);
                    }}
                  >
                    调试服务数据
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={async () => {
                      console.log('强制刷新服务数据...');
                      if (rancherApi && selectedWorkspace) {
                        const workspace = workspaces.find(w => w.id === selectedWorkspace);
                        if (workspace) {
                          try {
                            const servicesData = await rancherApi.getServices(workspace.clusterId);
                            console.log('All services:', servicesData);
                            const filteredServices = servicesData.filter(svc =>
                              workspace.namespaces.includes(svc.namespace)
                            );
                            console.log('Filtered services:', filteredServices);
                            setServices(filteredServices);
                          } catch (error) {
                            console.error('Failed to load services:', error);
                          }
                        }
                      }
                    }}
                  >
                    强制刷新服务
                  </Button>
                </Box>
              </Box>

              {services && services.length > 0 ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>服务名称</TableCell>
                        <TableCell>命名空间</TableCell>
                        <TableCell>类型</TableCell>
                        <TableCell>端口</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {services.map((service, index) => {
                        try {
                          // 安全地处理端口数据
                          let portsDisplay = '无';
                          if (service.ports) {
                            if (typeof service.ports === 'string') {
                              portsDisplay = service.ports;
                            } else if (Array.isArray(service.ports)) {
                              portsDisplay = service.ports.map(port => {
                                if (typeof port === 'object' && port !== null) {
                                  return `${port.port || '?'}/${port.protocol || 'TCP'}`;
                                }
                                return String(port);
                              }).join(', ');
                            } else if (typeof service.ports === 'object' && service.ports !== null) {
                              portsDisplay = `${service.ports.port || '?'}/${service.ports.protocol || 'TCP'}`;
                            } else {
                              portsDisplay = String(service.ports);
                            }
                          }

                          return (
                            <TableRow key={service.id || `service-${index}`}>
                              <TableCell>{String(service.name || '未知')}</TableCell>
                              <TableCell>
                                <Chip
                                  label={String(service.namespace || '默认')}
                                  size="small"
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={String(service.type || 'ClusterIP')}
                                  size="small"
                                  color={service.type === 'LoadBalancer' ? 'primary' : 'default'}
                                />
                              </TableCell>
                              <TableCell>{portsDisplay}</TableCell>
                            </TableRow>
                          );
                        } catch (error) {
                          console.error('Error rendering service row:', error, service);
                          return (
                            <TableRow key={`error-${index}`}>
                              <TableCell colSpan={4}>
                                <Typography color="error">
                                  渲染服务 {service?.name || index} 时出错
                                </Typography>
                              </TableCell>
                            </TableRow>
                          );
                        }
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    暂无服务数据
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    当前工作区没有找到服务，或者数据正在加载中
                  </Typography>
                </Paper>
              )}
            </Box>
          </TabPanel>

          {/* Pod 监控标签页 */}
          <TabPanel value={currentTab} index={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5">Pod 监控</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    console.log('=== Pod 监控调试信息 ===');
                    console.log('Pods:', pods);
                    console.log('Pods length:', pods?.length);

                    // 详细分析每个 Pod 的状态
                    pods?.forEach((pod, index) => {
                      console.log(`Pod ${index}: ${pod.name}`);
                      console.log(`  Status: "${pod.status}" (type: ${typeof pod.status})`);
                      console.log(`  Namespace: ${pod.namespace}`);
                      console.log(`  Node: ${pod.node}`);
                    });

                    console.log('Running pods:', pods?.filter(p => p.status === 'Running'));
                    console.log('Pending pods:', pods?.filter(p => p.status === 'Pending'));
                    console.log('Failed pods:', pods?.filter(p => p.status === 'Failed'));

                    // 检查所有可能的状态值
                    const allStatuses = [...new Set(pods?.map(p => p.status))];
                    console.log('All unique statuses:', allStatuses);
                  }}
                >
                  调试 Pod 数据
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<Refresh />}
                  onClick={async () => {
                    if (rancherApi && selectedWorkspace) {
                      const workspace = workspaces.find(w => w.id === selectedWorkspace);
                      if (workspace) {
                        try {
                          const podsData = await rancherApi.getPods(workspace.clusterId);
                          const filteredPods = podsData.filter(pod =>
                            workspace.namespaces.includes(pod.namespace)
                          );
                          setPods(filteredPods);
                          console.log('Pod 数据刷新成功');
                        } catch (error) {
                          console.error('Failed to refresh pods:', error);
                        }
                      }
                    }
                  }}
                >
                  刷新 Pod
                </Button>
              </Box>
            </Box>

            {/* Pod 统计卡片 */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={6} sm={3}>
                <Card sx={{ height: '100px' }}>
                  <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
                    <CheckCircle sx={{ fontSize: 24, color: 'success.main', mb: 0.5 }} />
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {pods?.filter(p => {
                        console.log(`Pod ${p.name} status: "${p.status}"`);
                        return p.status === 'Running';
                      }).length || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">运行中</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card sx={{ height: '100px' }}>
                  <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
                    <Warning sx={{ fontSize: 24, color: 'warning.main', mb: 0.5 }} />
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {pods?.filter(p => p.status === 'Pending').length || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">等待中</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card sx={{ height: '100px' }}>
                  <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
                    <ErrorIcon sx={{ fontSize: 24, color: 'error.main', mb: 0.5 }} />
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {pods?.filter(p => p.status === 'Failed' || p.status === 'Unknown').length || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">失败</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card sx={{ height: '100px' }}>
                  <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
                    <Info sx={{ fontSize: 24, color: 'info.main', mb: 0.5 }} />
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {pods?.length || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">总数</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Pod 列表 */}
            {pods && pods.length > 0 ? (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Pod 名称</TableCell>
                      <TableCell>命名空间</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>节点</TableCell>
                      <TableCell>容器</TableCell>
                      <TableCell>IP 地址</TableCell>
                      <TableCell>创建时间</TableCell>
                      <TableCell>操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {pods.map((pod, index) => {
                      try {
                        return (
                          <TableRow key={pod.id || `pod-${index}`} hover>
                            <TableCell>
                              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                {pod.name || '未知'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip label={pod.namespace || '默认'} size="small" variant="outlined" />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={getStatusText(pod.status) || '未知'}
                                color={getPodStatusColor(pod.status)}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {pod.node || '未分配'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box>
                                {pod.containers && pod.containers.length > 0 ? (
                                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                    {pod.containers.slice(0, 2).map((container, idx) => (
                                      <Box key={idx} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <Chip
                                          label={container.name}
                                          size="small"
                                          color={container.ready ? 'success' : 'warning'}
                                          variant="outlined"
                                        />
                                      </Box>
                                    ))}
                                    {pod.containers.length > 2 && (
                                      <Typography variant="caption" color="text.secondary">
                                        +{pod.containers.length - 2} 更多
                                      </Typography>
                                    )}
                                  </Box>
                                ) : (
                                  <Typography variant="caption" color="text.secondary">
                                    无容器信息
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                {pod.podIP || '未分配'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {pod.created ? new Date(pod.created).toLocaleString() : '未知'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Tooltip title="查看详情">
                                  <IconButton
                                    size="small"
                                    color="primary"
                                    onClick={() => {
                                      console.log('Pod 详情:', pod);
                                      setSelectedApp(pod);
                                      setDetailDialogOpen(true);
                                    }}
                                  >
                                    <Visibility />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="查看日志">
                                  <IconButton
                                    size="small"
                                    color="info"
                                    onClick={() => {
                                      console.log('查看 Pod 日志:', pod.name);
                                      // TODO: 实现日志查看功能
                                    }}
                                  >
                                    <Description />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="删除 Pod">
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => {
                                      if (window.confirm(`确定要删除 Pod "${pod.name}" 吗？`)) {
                                        console.log('删除 Pod:', pod.name);
                                        // TODO: 实现 Pod 删除功能
                                      }
                                    }}
                                  >
                                    <Delete />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </TableCell>
                          </TableRow>
                        );
                      } catch (error) {
                        console.error('Error rendering pod row:', error, pod);
                        return (
                          <TableRow key={`error-${index}`}>
                            <TableCell colSpan={8}>
                              <Typography color="error">
                                渲染 Pod {pod?.name || index} 时出错
                              </Typography>
                            </TableCell>
                          </TableRow>
                        );
                      }
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  暂无 Pod 数据
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  当前工作区没有找到 Pod，或者数据正在加载中
                </Typography>
              </Paper>
            )}
          </TabPanel>

          {/* 配置管理标签页 */}
          <TabPanel value={currentTab} index={3}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5">配置管理</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    console.log('=== 配置管理调试信息 ===');
                    console.log('ConfigMaps:', configMaps);
                    console.log('Secrets:', secrets);
                  }}
                >
                  调试配置数据
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<Refresh />}
                  onClick={async () => {
                    if (rancherApi && selectedWorkspace) {
                      const workspace = workspaces.find(w => w.id === selectedWorkspace);
                      if (workspace) {
                        try {
                          // 刷新 ConfigMaps
                          const configMapsData = await rancherApi.getConfigMaps(workspace.clusterId);
                          const filteredConfigMaps = configMapsData.filter(cm =>
                            workspace.namespaces.includes(cm.namespace)
                          );
                          setConfigMaps(filteredConfigMaps);

                          // 刷新 Secrets
                          const secretsData = await rancherApi.getSecrets(workspace.clusterId);
                          const filteredSecrets = secretsData.filter(secret =>
                            workspace.namespaces.includes(secret.namespace)
                          );
                          setSecrets(filteredSecrets);

                          console.log('配置数据刷新成功');
                        } catch (error) {
                          console.error('Failed to refresh config data:', error);
                        }
                      }
                    }
                  }}
                >
                  刷新配置
                </Button>
              </Box>
            </Box>

            {/* 配置统计 */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={6}>
                <Card sx={{ height: '100px' }}>
                  <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
                    <Settings sx={{ fontSize: 24, color: 'primary.main', mb: 0.5 }} />
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {configMaps?.length || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">ConfigMaps</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card sx={{ height: '100px' }}>
                  <CardContent sx={{ textAlign: 'center', py: 1.5 }}>
                    <Security sx={{ fontSize: 24, color: 'warning.main', mb: 0.5 }} />
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {secrets?.length || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">Secrets</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Grid container spacing={3}>
              {/* ConfigMaps */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Settings sx={{ mr: 1 }} />
                      ConfigMaps ({configMaps?.length || 0})
                    </Typography>

                    {configMaps && configMaps.length > 0 ? (
                      <List>
                        {configMaps.map((configMap, index) => (
                          <ListItem key={configMap.id || index} divider>
                            <ListItemIcon>
                              <Settings color="primary" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="subtitle2" sx={{ fontFamily: 'monospace' }}>
                                  {configMap.name}
                                </Typography>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="caption" color="text.secondary">
                                    命名空间: {configMap.namespace}
                                  </Typography>
                                  <br />
                                  <Typography variant="caption" color="text.secondary">
                                    数据项: {configMap.dataCount} 个
                                  </Typography>
                                  <br />
                                  <Typography variant="caption" color="text.secondary">
                                    创建时间: {configMap.created ? new Date(configMap.created).toLocaleString() : '未知'}
                                  </Typography>
                                </Box>
                              }
                            />
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="查看详情">
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => {
                                    console.log('ConfigMap 详情:', configMap);
                                  }}
                                >
                                  <Visibility />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="编辑">
                                <IconButton
                                  size="small"
                                  color="secondary"
                                  onClick={() => {
                                    console.log('编辑 ConfigMap:', configMap.name);
                                  }}
                                >
                                  <Edit />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          暂无 ConfigMaps
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Secrets */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Security sx={{ mr: 1 }} />
                      Secrets ({secrets?.length || 0})
                    </Typography>

                    {secrets && secrets.length > 0 ? (
                      <List>
                        {secrets.map((secret, index) => (
                          <ListItem key={secret.id || index} divider>
                            <ListItemIcon>
                              <Security color="warning" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="subtitle2" sx={{ fontFamily: 'monospace' }}>
                                  {secret.name}
                                </Typography>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="caption" color="text.secondary">
                                    命名空间: {secret.namespace}
                                  </Typography>
                                  <br />
                                  <Typography variant="caption" color="text.secondary">
                                    类型: {secret.type}
                                  </Typography>
                                  <br />
                                  <Typography variant="caption" color="text.secondary">
                                    数据项: {secret.dataCount} 个
                                  </Typography>
                                  <br />
                                  <Typography variant="caption" color="text.secondary">
                                    创建时间: {secret.created ? new Date(secret.created).toLocaleString() : '未知'}
                                  </Typography>
                                </Box>
                              }
                            />
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="查看详情">
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => {
                                    console.log('Secret 详情:', secret);
                                  }}
                                >
                                  <Visibility />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="编辑">
                                <IconButton
                                  size="small"
                                  color="secondary"
                                  onClick={() => {
                                    console.log('编辑 Secret:', secret.name);
                                  }}
                                >
                                  <Edit />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          暂无 Secrets
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </Box>
      )}

      {/* 部署应用对话框 */}
      <Dialog open={deployDialogOpen} onClose={() => setDeployDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>部署新应用</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="应用名称"
                value={deployForm.name}
                onChange={(e) => setDeployForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="容器镜像"
                value={deployForm.image}
                onChange={(e) => setDeployForm(prev => ({ ...prev, image: e.target.value }))}
                placeholder="例如: nginx:1.21"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>命名空间</InputLabel>
                <Select
                  value={deployForm.namespace}
                  label="命名空间"
                  onChange={(e) => setDeployForm(prev => ({ ...prev, namespace: e.target.value }))}
                >
                  {currentWorkspace?.namespaces?.map((ns) => (
                    <MenuItem key={ns} value={ns}>{ns}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="副本数"
                type="number"
                value={deployForm.replicas}
                onChange={(e) => setDeployForm(prev => ({ ...prev, replicas: parseInt(e.target.value) }))}
                inputProps={{ min: 1, max: 10 }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeployDialogOpen(false)}>取消</Button>
          <Button onClick={handleSaveDeploy} variant="contained" disabled={loading}>
            {loading ? '部署中...' : '部署'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 应用详情对话框 */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => {
          console.log('Closing detail dialog');
          setDetailDialogOpen(false);
          setSelectedApp(null);
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          应用详情 - {selectedApp?.name || '未知应用'}
        </DialogTitle>
        <DialogContent>
          {selectedApp ? (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>基本信息</Typography>
                  <Box sx={{ pl: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>名称:</strong> {selectedApp.name || '未知'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>命名空间:</strong> {selectedApp.namespace || '未知'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>状态:</strong> {getStatusText(selectedApp.status) || '未知'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>副本数:</strong> {selectedApp.replicas || '0/0'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>镜像:</strong> {selectedApp.image || '未知'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>创建时间:</strong> {selectedApp.created || '未知'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>标签</Typography>
                  <Box sx={{ pl: 2 }}>
                    {selectedApp.labels && Object.keys(selectedApp.labels).length > 0 ? (
                      Object.entries(selectedApp.labels).map(([key, value]) => (
                        <Chip
                          key={key}
                          label={`${key}: ${value}`}
                          size="small"
                          sx={{ mr: 1, mb: 1 }}
                          variant="outlined"
                        />
                      ))
                    ) : (
                      <Typography variant="body2" color="text.secondary">无标签</Typography>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Box>
          ) : (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                没有选择应用
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setDetailDialogOpen(false);
            setSelectedApp(null);
          }}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 应用编辑对话框 */}
      <Dialog
        open={editDialogOpen}
        onClose={() => {
          console.log('Closing edit dialog');
          setEditDialogOpen(false);
          setSelectedApp(null);
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          编辑应用 - {selectedApp?.name || '未知应用'}
        </DialogTitle>
        <DialogContent>
          {selectedApp ? (
            <Box sx={{ mt: 2 }}>
              <Alert severity="info" sx={{ mb: 2 }}>
                当前版本支持副本数调整，更多编辑功能正在开发中。
              </Alert>
              <TextField
                label="副本数"
                type="number"
                fullWidth
                defaultValue={
                  selectedApp.replicas
                    ? (selectedApp.replicas.includes('/')
                        ? selectedApp.replicas.split('/')[1]
                        : selectedApp.replicas)
                    : 1
                }
                inputProps={{ min: 0, max: 10 }}
                helperText="调整应用的副本数量"
                sx={{ mb: 2 }}
              />
              <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>当前状态:</strong> {getStatusText(selectedApp.status)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>当前副本:</strong> {selectedApp.replicas}
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                没有选择应用
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setEditDialogOpen(false);
            setSelectedApp(null);
          }}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              // TODO: 实现副本数调整
              console.log('编辑功能开发中...', selectedApp);
              setEditDialogOpen(false);
              setSelectedApp(null);
            }}
            disabled={!selectedApp}
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>

      {/* Rancher 配置对话框 */}
      <ConfigDialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        onSave={handleConfigSave}
        initialConfig={null}
      />
    </Container>
  );
};

export default WorkspaceOperationPanel;
