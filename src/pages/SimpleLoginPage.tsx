import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { Login as LoginIcon, Security } from '@mui/icons-material';
import { simpleAuthService } from '../services/simpleAuth';

interface Props {
  onLoginSuccess: () => void;
}

const SimpleLoginPage: React.FC<Props> = ({ onLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await simpleAuthService.login(username, password);
      onLoginSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async (demoUsername: string, demoPassword: string) => {
    setLoading(true);
    setError(null);

    try {
      await simpleAuthService.login(demoUsername, demoPassword);
      onLoginSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      py: 4
    }}>
      <Box sx={{ width: '100%' }}>
        {/* 标题 */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Security sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          <Typography variant="h3" component="h1" gutterBottom>
            KubeX
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Kubernetes 管理平台
          </Typography>
        </Box>

        {/* 登录表单 */}
        <Paper elevation={3} sx={{ p: 4, mb: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ textAlign: 'center' }}>
            用户登录
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="用户名"
              variant="outlined"
              margin="normal"
              required
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={loading}
            />

            <TextField
              fullWidth
              label="密码"
              type="password"
              variant="outlined"
              margin="normal"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Box>
        </Paper>

        {/* 演示账户 */}
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
            演示账户
          </Typography>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={() => handleDemoLogin('admin', 'admin123')}
              disabled={loading}
              fullWidth
            >
              管理员登录 (admin / admin123)
            </Button>
            
            <Button
              variant="outlined"
              onClick={() => handleDemoLogin('operator1', 'op123')}
              disabled={loading}
              fullWidth
            >
              操作员登录 (operator1 / op123)
            </Button>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 2 }}>
            管理员可以管理所有功能，操作员只能访问分配的工作区
          </Typography>
        </Paper>
      </Box>
    </Container>
  );
};

export default SimpleLoginPage;
