
// src/pages/Dashboard.tsx

import React, { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Button,
  Container,
  Grid,
  CircularProgress,
  Snackbar,
  Alert,
  Box,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import { Settings, Dashboard as DashboardIcon, Apps, CloudUpload, Monitor } from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import type {
  RancherConfig,
  Deployment,
  Namespace,
  WorkspaceConfig,
  ResourceMetrics,
  Pod
} from '../services/rancherApi';
import ConfigDialog from '../components/ConfigDialog';
import WorkspaceSelector from '../components/WorkspaceSelector';
import ResourceMonitor from '../components/ResourceMonitor';
import ApplicationManager from '../components/ApplicationManager';
import ApplicationDeployment from '../components/ApplicationDeployment';

const Dashboard: React.FC = () => {
  const [config, setConfig] = useState<RancherConfig | null>(null);
  const [api, setApi] = useState<RancherAPI | null>(null);
  const [clusters, setClusters] = useState<any[]>([]);
  const [selectedCluster, setSelectedCluster] = useState<string>('');

  // 工作区管理
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>('');
  const [namespaces, setNamespaces] = useState<Namespace[]>([]);

  // 资源数据
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [pods, setPods] = useState<Pod[]>([]);
  const [metrics, setMetrics] = useState<ResourceMetrics>({
    cpu: { used: '0m', total: '0m', percentage: 0 },
    memory: { used: '0Mi', total: '0Mi', percentage: 0 },
    pods: { running: 0, pending: 0, failed: 0, total: 0 }
  });

  // UI 状态
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configOpen, setConfigOpen] = useState(false);

  useEffect(() => {
    const savedConfig = localStorage.getItem('rancherConfig');
    if (savedConfig) {
      const parsedConfig: RancherConfig = JSON.parse(savedConfig);
      setConfig(parsedConfig);
      const rancherApi = new RancherAPI(parsedConfig);
      setApi(rancherApi);
      if (parsedConfig.clusterId) {
        setSelectedCluster(parsedConfig.clusterId);
      }
    }

    // 加载保存的工作区
    const savedWorkspaces = localStorage.getItem('workspaces');
    if (savedWorkspaces) {
      const parsedWorkspaces: WorkspaceConfig[] = JSON.parse(savedWorkspaces);
      setWorkspaces(parsedWorkspaces);
      if (parsedWorkspaces.length > 0) {
        setSelectedWorkspace(parsedWorkspaces[0].id);
      }
    }
  }, []);

  const handleConfigSave = async (newConfig: RancherConfig) => {
    setLoading(true);
    setError(null);

    console.log('Attempting to connect with config:', {
      baseUrl: newConfig.baseUrl,
      tokenLength: newConfig.token?.length || 0
    });

    const rancherApi = new RancherAPI(newConfig);
    const connected = await rancherApi.testConnection();

    if (connected) {
      setConfig(newConfig);
      setApi(rancherApi);
      localStorage.setItem('rancherConfig', JSON.stringify(newConfig));
      setConfigOpen(false);
      loadClusters(rancherApi);
    } else {
      setError('Connection failed. Please check URL and Token. See browser console for details.');
    }
    setLoading(false);
  };

  // 工作区管理
  const handleWorkspaceCreate = (workspace: Omit<WorkspaceConfig, 'id' | 'created'>) => {
    const newWorkspace: WorkspaceConfig = {
      ...workspace,
      id: `workspace-${Date.now()}`,
      created: new Date().toISOString()
    };
    const updatedWorkspaces = [...workspaces, newWorkspace];
    setWorkspaces(updatedWorkspaces);
    localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));
    setSelectedWorkspace(newWorkspace.id);
  };

  const handleWorkspaceUpdate = (workspace: WorkspaceConfig) => {
    const updatedWorkspaces = workspaces.map(w => w.id === workspace.id ? workspace : w);
    setWorkspaces(updatedWorkspaces);
    localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));
  };

  const handleWorkspaceDelete = (workspaceId: string) => {
    const updatedWorkspaces = workspaces.filter(w => w.id !== workspaceId);
    setWorkspaces(updatedWorkspaces);
    localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));
    if (selectedWorkspace === workspaceId && updatedWorkspaces.length > 0) {
      setSelectedWorkspace(updatedWorkspaces[0].id);
    }
  };

  const handleWorkspaceChange = (workspaceId: string) => {
    setSelectedWorkspace(workspaceId);
    // 重新加载当前工作区的数据
    if (api && selectedCluster) {
      loadWorkspaceData();
    }
  };
  
  const loadClusters = useCallback(async (rancherApi: RancherAPI) => {
    setLoading(true);
    try {
      const clusterData = await rancherApi.getClusters();
      setClusters(clusterData);
      if (clusterData.length > 0 && !selectedCluster) {
        setSelectedCluster(clusterData[0].id);
      }
      // 加载命名空间
      if (clusterData.length > 0) {
        const clusterId = selectedCluster || clusterData[0].id;
        const namespaceData = await rancherApi.getNamespaces(clusterId);
        setNamespaces(namespaceData);
      }
    } catch (e) {
      setError('Failed to load clusters.');
    }
    setLoading(false);
  }, [selectedCluster]);

  const loadWorkspaceData = useCallback(async () => {
    if (!api || !selectedCluster || !selectedWorkspace) return;

    setLoading(true);
    setError(null);

    try {
      const currentWorkspace = workspaces.find(w => w.id === selectedWorkspace);
      if (!currentWorkspace) return;

      // 加载部署
      const allDeployments = await api.getDeployments(selectedCluster);
      const workspaceDeployments = allDeployments.filter(d =>
        currentWorkspace.namespaces.includes(d.namespace)
      );
      setDeployments(workspaceDeployments);

      // 加载 Pods
      const allPods: Pod[] = [];
      for (const namespace of currentWorkspace.namespaces) {
        try {
          const namespacePods = await api.getPods(selectedCluster, namespace);
          allPods.push(...namespacePods);
        } catch (e) {
          console.warn(`Failed to load pods for namespace ${namespace}:`, e);
        }
      }
      setPods(allPods);

      // 加载资源指标
      const resourceMetrics = await api.getResourceMetrics(selectedCluster);
      setMetrics(resourceMetrics);

    } catch (e) {
      setError('Failed to load workspace data.');
      console.error('Load workspace data error:', e);
    }
    setLoading(false);
  }, [api, selectedCluster, selectedWorkspace, workspaces]);

  useEffect(() => {
    if (api && !clusters.length) {
      loadClusters(api);
    }
  }, [api, clusters, loadClusters]);

  useEffect(() => {
    if (selectedCluster && api && selectedWorkspace) {
      loadWorkspaceData();
    }
  }, [selectedCluster, api, selectedWorkspace, loadWorkspaceData]);

  // 应用管理函数
  const handleAppStart = async (namespace: string, name: string, replicas: number = 1) => {
    if (!api || !selectedCluster) return;
    await api.startDeployment(selectedCluster, namespace, name, replicas);
    loadWorkspaceData();
  };

  const handleAppStop = async (namespace: string, name: string) => {
    if (!api || !selectedCluster) return;
    await api.stopDeployment(selectedCluster, namespace, name);
    loadWorkspaceData();
  };

  const handleAppDelete = async (namespace: string, name: string) => {
    if (!api || !selectedCluster) return;
    await api.deleteDeployment(selectedCluster, namespace, name);
    loadWorkspaceData();
  };

  const handleAppRestart = async (namespace: string, name: string) => {
    if (!api || !selectedCluster) return;
    await api.restartDeployment(selectedCluster, namespace, name);
    loadWorkspaceData();
  };

  const handleAppScale = async (namespace: string, name: string, replicas: number) => {
    if (!api || !selectedCluster) return;
    await api.scaleDeployment(selectedCluster, namespace, name, replicas);
    loadWorkspaceData();
  };

  const handleAppDeploy = async (config: any) => {
    if (!api || !selectedCluster) return;
    await api.deployApplication(selectedCluster, config.namespace, {
      name: config.name,
      image: config.image,
      replicas: config.replicas,
      ports: config.ports,
      env: config.env,
      resources: config.resources,
      labels: config.labels
    });
    loadWorkspaceData();
  };

  const currentWorkspace = workspaces.find(w => w.id === selectedWorkspace);
  const workspaceNamespaces = currentWorkspace?.namespaces || [];

  return (
    <>
      <Container maxWidth="xl" sx={{ mt: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Kubernetes 管理平台
          </Typography>
          <Button
            variant="contained"
            onClick={() => setConfigOpen(true)}
            startIcon={<Settings />}
          >
            配置连接
          </Button>
        </Box>

        {!config ? (
          <Alert severity="info">请先配置 Rancher API 连接。</Alert>
        ) : (
          <>
            {/* 调试信息 */}
            <Alert severity="info" sx={{ mb: 2 }}>
              调试信息: 集群数量: {clusters.length}, 命名空间数量: {namespaces.length}, 工作区数量: {workspaces.length}, 当前工作区: {selectedWorkspace || '未选择'}
            </Alert>

            {/* 工作区选择器 */}
            <WorkspaceSelector
              workspaces={workspaces}
              selectedWorkspace={selectedWorkspace}
              onWorkspaceChange={handleWorkspaceChange}
              onWorkspaceCreate={handleWorkspaceCreate}
              onWorkspaceUpdate={handleWorkspaceUpdate}
              onWorkspaceDelete={handleWorkspaceDelete}
              availableNamespaces={namespaces}
              loading={loading}
            />

            {selectedWorkspace && (
              <>
                {/* 标签页导航 */}
                <Paper sx={{ mb: 3 }}>
                  <Tabs
                    value={currentTab}
                    onChange={(_, newValue) => setCurrentTab(newValue)}
                    variant="fullWidth"
                  >
                    <Tab icon={<DashboardIcon />} label="概览" />
                    <Tab icon={<Monitor />} label="监控" />
                    <Tab icon={<Apps />} label="应用管理" />
                    <Tab icon={<CloudUpload />} label="应用部署" />
                  </Tabs>
                </Paper>

                {loading && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                    <CircularProgress />
                  </Box>
                )}

                {/* 标签页内容 */}
                {currentTab === 0 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={8}>
                      <ResourceMonitor
                        metrics={metrics}
                        pods={pods}
                        onRefresh={loadWorkspaceData}
                        loading={loading}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          工作区信息
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {currentWorkspace?.description || '暂无描述'}
                        </Typography>
                        <Typography variant="body2">
                          命名空间数量: {workspaceNamespaces.length}
                        </Typography>
                        <Typography variant="body2">
                          应用数量: {deployments.length}
                        </Typography>
                        <Typography variant="body2">
                          Pod 数量: {pods.length}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                )}

                {currentTab === 1 && (
                  <ResourceMonitor
                    metrics={metrics}
                    pods={pods}
                    onRefresh={loadWorkspaceData}
                    loading={loading}
                  />
                )}

                {currentTab === 2 && (
                  <ApplicationManager
                    deployments={deployments}
                    onStart={handleAppStart}
                    onStop={handleAppStop}
                    onDelete={handleAppDelete}
                    onRestart={handleAppRestart}
                    onScale={handleAppScale}
                    loading={loading}
                  />
                )}

                {currentTab === 3 && (
                  <ApplicationDeployment
                    namespaces={workspaceNamespaces}
                    onDeploy={handleAppDeploy}
                    loading={loading}
                  />
                )}
              </>
            )}
          </>
        )}
      </Container>
      <ConfigDialog
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        onSave={handleConfigSave}
        loading={loading}
        error={error}
        currentConfig={config}
      />
      <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </>
  );
};

export default Dashboard;
