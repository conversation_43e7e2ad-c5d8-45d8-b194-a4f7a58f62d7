import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Container,
  Alert,
  Box,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid
} from '@mui/material';
import { Settings } from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import type { RancherConfig, WorkspaceConfig } from '../services/rancherApi';
import ConfigDialog from '../components/ConfigDialog';
import { simpleAuthService } from '../services/simpleAuth';

const SimpleDashboard: React.FC = () => {
  const [config, setConfig] = useState<RancherConfig | null>(null);
  const [api, setApi] = useState<RancherAPI | null>(null);
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configOpen, setConfigOpen] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('未连接');

  // 获取当前用户
  const currentUser = simpleAuthService.getCurrentUser();

  useEffect(() => {
    const savedConfig = localStorage.getItem('rancherConfig');
    if (savedConfig) {
      const parsedConfig: RancherConfig = JSON.parse(savedConfig);
      setConfig(parsedConfig);
      const rancherApi = new RancherAPI(parsedConfig);
      setApi(rancherApi);
      testConnection(rancherApi);
    }

    // 加载工作区
    const savedWorkspaces = localStorage.getItem('workspaces');
    if (savedWorkspaces) {
      const parsedWorkspaces: WorkspaceConfig[] = JSON.parse(savedWorkspaces);
      setWorkspaces(parsedWorkspaces);

      // 根据用户权限过滤工作区
      const accessibleWorkspaces = currentUser?.role === 'admin'
        ? parsedWorkspaces
        : parsedWorkspaces.filter(w => w.id.includes(currentUser?.username || ''));

      if (accessibleWorkspaces.length > 0) {
        setSelectedWorkspace(accessibleWorkspaces[0].id);
      }
    }
  }, [currentUser]);

  const testConnection = async (rancherApi: RancherAPI) => {
    setLoading(true);
    try {
      const connected = await rancherApi.testConnection();
      setConnectionStatus(connected ? '已连接' : '连接失败');
    } catch (err) {
      setConnectionStatus('连接错误');
      setError(`连接测试失败: ${err.message}`);
    }
    setLoading(false);
  };

  const handleConfigSave = async (newConfig: RancherConfig) => {
    setLoading(true);
    setError(null);
    
    const rancherApi = new RancherAPI(newConfig);
    const connected = await rancherApi.testConnection();
    
    if (connected) {
      setConfig(newConfig);
      setApi(rancherApi);
      localStorage.setItem('rancherConfig', JSON.stringify(newConfig));
      setConfigOpen(false);
      setConnectionStatus('已连接');
    } else {
      setError('连接失败，请检查 URL 和 Token');
      setConnectionStatus('连接失败');
    }
    setLoading(false);
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Kubernetes 管理平台
        </Typography>
        <Button
          variant="contained"
          onClick={() => setConfigOpen(true)}
          startIcon={<Settings />}
        >
          配置连接
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 用户信息和连接状态 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              用户信息
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="body1">
                当前用户: <strong>{currentUser?.username}</strong>
              </Typography>
              <Chip
                label={currentUser?.role === 'admin' ? '管理员' : '操作员'}
                color={currentUser?.role === 'admin' ? 'primary' : 'secondary'}
                size="small"
              />
            </Box>
            <Typography variant="body2" color="text.secondary">
              权限: {currentUser?.role === 'admin' ? '全部权限' : '限定权限'}
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              连接状态
            </Typography>
            <Typography variant="body1" color={connectionStatus === '已连接' ? 'success.main' : 'error.main'}>
              {connectionStatus}
            </Typography>
            {config && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  服务器: {config.baseUrl}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Token: {config.token ? '已配置' : '未配置'}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* 工作区选择 */}
      {connectionStatus === '已连接' && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            工作区选择
          </Typography>

          {workspaces.length === 0 ? (
            <Alert severity="info">
              {currentUser?.role === 'admin'
                ? '暂无工作区。请在工作区管理页面创建工作区。'
                : '您还没有被分配到任何工作区。请联系管理员分配工作区权限。'
              }
            </Alert>
          ) : (
            <Box>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>选择工作区</InputLabel>
                <Select
                  value={selectedWorkspace}
                  label="选择工作区"
                  onChange={(e) => setSelectedWorkspace(e.target.value)}
                >
                  {workspaces
                    .filter(workspace =>
                      currentUser?.role === 'admin' ||
                      workspace.id.includes(currentUser?.username || '')
                    )
                    .map((workspace) => (
                      <MenuItem key={workspace.id} value={workspace.id}>
                        {workspace.name} ({workspace.namespaces?.length || 0} 个命名空间)
                      </MenuItem>
                    ))
                  }
                </Select>
              </FormControl>

              {selectedWorkspace && (
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    当前工作区: {workspaces.find(w => w.id === selectedWorkspace)?.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    描述: {workspaces.find(w => w.id === selectedWorkspace)?.description || '暂无描述'}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </Paper>
      )}

      {!config && (
        <Alert severity="info">
          请先配置 Rancher API 连接以开始使用。
        </Alert>
      )}

      {config && connectionStatus === '已连接' && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            功能模块
          </Typography>
          <Typography variant="body1">
            连接成功！您现在可以使用以下功能：
          </Typography>
          <Box component="ul" sx={{ mt: 2 }}>
            <li>工作区管理</li>
            <li>资源监控</li>
            <li>应用管理</li>
            <li>应用部署</li>
          </Box>
        </Paper>
      )}

      <ConfigDialog
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        onSave={handleConfigSave}
        loading={loading}
        error={error}
        currentConfig={config}
      />
    </Container>
  );
};

export default SimpleDashboard;
