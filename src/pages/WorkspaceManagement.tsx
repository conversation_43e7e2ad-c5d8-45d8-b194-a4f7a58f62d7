import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  IconButton,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Folder,
  Settings,
  Apps,
  Storage
} from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import type { RancherConfig, Namespace, WorkspaceConfig } from '../services/rancherApi';
import ConfigDialog from '../components/ConfigDialog';
import { authService } from '../services/authService';
import { Permission, hasPermission, UserRole } from '../types/auth';

const WorkspaceManagement: React.FC = () => {
  const [config, setConfig] = useState<RancherConfig | null>(null);
  const [api, setApi] = useState<RancherAPI | null>(null);
  const [namespaces, setNamespaces] = useState<Namespace[]>([]);
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [configOpen, setConfigOpen] = useState(false);

  // 获取当前用户
  const currentUser = authService.getCurrentUser();
  
  // 工作区编辑对话框
  const [workspaceDialogOpen, setWorkspaceDialogOpen] = useState(false);
  const [editingWorkspace, setEditingWorkspace] = useState<WorkspaceConfig | null>(null);
  const [workspaceForm, setWorkspaceForm] = useState({
    name: '',
    description: '',
    namespaces: [] as string[]
  });

  useEffect(() => {
    const savedConfig = localStorage.getItem('rancherConfig');
    if (savedConfig) {
      const parsedConfig: RancherConfig = JSON.parse(savedConfig);
      setConfig(parsedConfig);
      const rancherApi = new RancherAPI(parsedConfig);
      setApi(rancherApi);
      loadData(rancherApi);
    }

    // 加载保存的工作区
    const savedWorkspaces = localStorage.getItem('workspaces');
    if (savedWorkspaces) {
      const parsedWorkspaces: WorkspaceConfig[] = JSON.parse(savedWorkspaces);
      setWorkspaces(parsedWorkspaces);
    }
  }, []);

  const loadData = async (rancherApi: RancherAPI) => {
    setLoading(true);
    try {
      const clusters = await rancherApi.getClusters();
      if (clusters.length > 0) {
        const namespaceData = await rancherApi.getNamespaces(clusters[0].id);
        setNamespaces(namespaceData);
      }
    } catch (err) {
      setError('加载数据失败: ' + err.message);
    }
    setLoading(false);
  };

  const handleConfigSave = async (newConfig: RancherConfig) => {
    setLoading(true);
    setError(null);
    
    const rancherApi = new RancherAPI(newConfig);
    const connected = await rancherApi.testConnection();
    
    if (connected) {
      setConfig(newConfig);
      setApi(rancherApi);
      localStorage.setItem('rancherConfig', JSON.stringify(newConfig));
      setConfigOpen(false);
      loadData(rancherApi);
    } else {
      setError('连接失败，请检查 URL 和 Token');
    }
    setLoading(false);
  };

  const handleCreateWorkspace = () => {
    // 检查权限
    if (!hasPermission(currentUser, Permission.CREATE_WORKSPACE)) {
      setError('您没有创建工作区的权限');
      return;
    }

    setEditingWorkspace(null);
    setWorkspaceForm({ name: '', description: '', namespaces: [] });
    setWorkspaceDialogOpen(true);
  };

  const handleEditWorkspace = (workspace: WorkspaceConfig) => {
    // 检查权限
    if (!hasPermission(currentUser, Permission.EDIT_WORKSPACE)) {
      setError('您没有编辑工作区的权限');
      return;
    }

    setEditingWorkspace(workspace);
    setWorkspaceForm({
      name: workspace.name,
      description: workspace.description || '',
      namespaces: workspace.namespaces
    });
    setWorkspaceDialogOpen(true);
  };

  const handleDeleteWorkspace = (workspaceId: string) => {
    // 检查权限
    if (!hasPermission(currentUser, Permission.DELETE_WORKSPACE)) {
      setError('您没有删除工作区的权限');
      return;
    }

    if (window.confirm('确定要删除这个工作区吗？这不会删除实际的命名空间。')) {
      const updatedWorkspaces = workspaces.filter(w => w.id !== workspaceId);
      setWorkspaces(updatedWorkspaces);
      localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));

      // 记录操作日志
      if (currentUser) {
        authService.logAction(currentUser.id, '删除工作区', 'workspace', workspaceId);
      }
    }
  };

  const handleSaveWorkspace = () => {
    if (!workspaceForm.name.trim()) {
      setError('工作区名称不能为空');
      return;
    }

    if (workspaceForm.namespaces.length === 0) {
      setError('请至少选择一个命名空间');
      return;
    }

    let updatedWorkspaces;
    if (editingWorkspace) {
      // 编辑现有工作区
      updatedWorkspaces = workspaces.map(w => 
        w.id === editingWorkspace.id 
          ? { ...editingWorkspace, ...workspaceForm }
          : w
      );
    } else {
      // 创建新工作区
      const newWorkspace: WorkspaceConfig = {
        id: `workspace-${Date.now()}`,
        name: workspaceForm.name,
        description: workspaceForm.description,
        namespaces: workspaceForm.namespaces,
        created: new Date().toISOString(),
        labels: { 'app.kubernetes.io/managed-by': 'rancher-dashboard' }
      };
      updatedWorkspaces = [...workspaces, newWorkspace];
    }

    setWorkspaces(updatedWorkspaces);
    localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));
    setWorkspaceDialogOpen(false);
    setError(null);
  };

  const handleNamespaceToggle = (namespace: string) => {
    setWorkspaceForm(prev => ({
      ...prev,
      namespaces: prev.namespaces.includes(namespace)
        ? prev.namespaces.filter(ns => ns !== namespace)
        : [...prev.namespaces, namespace]
    }));
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            工作区管理
          </Typography>
          <Typography variant="body1" color="text.secondary">
            管理不同项目的工作区，每个工作区包含相关的命名空间
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={() => setConfigOpen(true)}
          startIcon={<Settings />}
        >
          配置连接
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {!config ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          请先配置 Rancher API 连接以开始管理工作区。
        </Alert>
      ) : (
        <>
          {/* 统计信息 */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, textAlign: 'center' }}>
                <Folder sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h4">{workspaces.length}</Typography>
                <Typography variant="body2" color="text.secondary">工作区总数</Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, textAlign: 'center' }}>
                <Storage sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h4">{namespaces.length}</Typography>
                <Typography variant="body2" color="text.secondary">可用命名空间</Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, textAlign: 'center' }}>
                <Apps sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                <Typography variant="h4">
                  {workspaces.reduce((sum, w) => sum + w.namespaces.length, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">已分配命名空间</Typography>
              </Paper>
            </Grid>
          </Grid>

          {/* 工作区列表 */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5">工作区列表</Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreateWorkspace}
              disabled={loading || namespaces.length === 0}
            >
              创建工作区
            </Button>
          </Box>

          {workspaces.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Folder sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                还没有工作区
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                创建工作区来组织和管理您的 Kubernetes 命名空间
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreateWorkspace}
                disabled={loading || namespaces.length === 0}
                sx={{ mt: 2 }}
              >
                创建第一个工作区
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {workspaces.map((workspace) => (
                <Grid item xs={12} md={6} lg={4} key={workspace.id}>
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Folder sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="h6" component="div">
                          {workspace.name}
                        </Typography>
                      </Box>
                      
                      {workspace.description && (
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {workspace.description}
                        </Typography>
                      )}

                      <Typography variant="body2" gutterBottom>
                        命名空间 ({workspace.namespaces.length}):
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                        {workspace.namespaces.slice(0, 3).map(ns => (
                          <Chip key={ns} label={ns} size="small" />
                        ))}
                        {workspace.namespaces.length > 3 && (
                          <Chip label={`+${workspace.namespaces.length - 3}`} size="small" variant="outlined" />
                        )}
                      </Box>

                      <Typography variant="caption" color="text.secondary">
                        创建时间: {new Date(workspace.created).toLocaleDateString()}
                      </Typography>
                    </CardContent>
                    
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<Edit />}
                        onClick={() => handleEditWorkspace(workspace)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="small"
                        color="error"
                        startIcon={<Delete />}
                        onClick={() => handleDeleteWorkspace(workspace.id)}
                      >
                        删除
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </>
      )}

      {/* 配置对话框 */}
      <ConfigDialog
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        onSave={handleConfigSave}
        loading={loading}
        error={error}
        currentConfig={config}
      />

      {/* 工作区编辑对话框 */}
      <Dialog open={workspaceDialogOpen} onClose={() => setWorkspaceDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingWorkspace ? '编辑工作区' : '创建工作区'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="工作区名称"
            fullWidth
            variant="outlined"
            value={workspaceForm.name}
            onChange={(e) => setWorkspaceForm(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2 }}
          />
          
          <TextField
            margin="dense"
            label="描述"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            value={workspaceForm.description}
            onChange={(e) => setWorkspaceForm(prev => ({ ...prev, description: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <Typography variant="subtitle2" gutterBottom>
            选择命名空间:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, maxHeight: 200, overflow: 'auto' }}>
            {namespaces.map((namespace) => (
              <Chip
                key={namespace.name}
                label={namespace.name}
                clickable
                color={workspaceForm.namespaces.includes(namespace.name) ? 'primary' : 'default'}
                onClick={() => handleNamespaceToggle(namespace.name)}
                variant={workspaceForm.namespaces.includes(namespace.name) ? 'filled' : 'outlined'}
              />
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWorkspaceDialogOpen(false)}>取消</Button>
          <Button onClick={handleSaveWorkspace} variant="contained">
            {editingWorkspace ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default WorkspaceManagement;
