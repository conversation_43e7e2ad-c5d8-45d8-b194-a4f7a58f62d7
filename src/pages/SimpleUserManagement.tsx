import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Add,
  AdminPanelSettings,
  Engineering,
  People
} from '@mui/icons-material';
import { simpleAuthService } from '../services/simpleAuth';

const SimpleUserManagement: React.FC = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [users, setUsers] = useState([]);
  const [workspaces, setWorkspaces] = useState([]);
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    email: '',
    role: 'operator' as 'admin' | 'operator',
    workspaceIds: [] as string[]
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 加载用户列表和工作区列表
  React.useEffect(() => {
    loadUsers();
    loadWorkspaces();
  }, []);

  const loadUsers = () => {
    try {
      const allUsers = simpleAuthService.getUsers();
      setUsers(allUsers);
    } catch (error) {
      console.error('Failed to load users:', error);
      setError('加载用户失败');
    }
  };

  const loadWorkspaces = () => {
    try {
      const workspaceConfigs = JSON.parse(localStorage.getItem('workspace_configs') || '[]');
      setWorkspaces(workspaceConfigs);
    } catch (error) {
      console.error('Failed to load workspaces:', error);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setNewUser({
      username: '',
      password: '',
      email: '',
      role: 'operator',
      workspaceIds: []
    });
    setError(null);
    setSuccess(null);
    setDialogOpen(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setNewUser({
      username: user.username,
      password: '',
      email: user.email || '',
      role: user.role,
      workspaceIds: user.workspaceIds || []
    });
    setError(null);
    setSuccess(null);
    setDialogOpen(true);
  };

  const handleSaveUser = () => {
    if (!newUser.username.trim()) {
      setError('用户名不能为空');
      return;
    }

    if (!editingUser && !newUser.password.trim()) {
      setError('新用户密码不能为空');
      return;
    }

    try {
      if (editingUser) {
        // 编辑现有用户
        const updateData = {
          username: newUser.username,
          email: newUser.email,
          role: newUser.role,
          ...(newUser.password.trim() && { password: newUser.password })
        };

        simpleAuthService.updateUser(editingUser.id, updateData);

        // 更新工作区分配
        updateUserWorkspaceAssignments(editingUser.id, newUser.workspaceIds);

        setError(null);
        setSuccess('用户信息更新成功');
        console.log('User updated successfully');
      } else {
        // 创建新用户
        const success = simpleAuthService.createUser({
          username: newUser.username,
          password: newUser.password,
          email: newUser.email,
          role: newUser.role,
          workspaceIds: newUser.workspaceIds
        });

        if (success) {
          setError(null);
          setSuccess(`用户 "${newUser.username}" 创建成功`);
          console.log('User created successfully:', newUser.username);

          // 验证用户是否真的创建成功
          setTimeout(() => {
            const allUsers = simpleAuthService.getUsers();
            const createdUser = allUsers.find(u => u.username === newUser.username);
            console.log('Verification - Created user found:', createdUser);
          }, 100);
        } else {
          throw new Error('用户创建失败');
        }
      }

      loadUsers();
      setDialogOpen(false);
      setEditingUser(null);
      setNewUser({ username: '', password: '', email: '', role: 'operator', workspaceIds: [] });
    } catch (error) {
      setError(error.message || '保存用户失败');
    }
  };

  const updateUserWorkspaceAssignments = (userId: string, newWorkspaceIds: string[]) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      const oldWorkspaceIds = user.workspaceIds || [];

      // 移除不再分配的工作区
      oldWorkspaceIds.forEach(workspaceId => {
        if (!newWorkspaceIds.includes(workspaceId)) {
          simpleAuthService.removeWorkspaceFromUser(userId, workspaceId);
        }
      });

      // 添加新分配的工作区
      newWorkspaceIds.forEach(workspaceId => {
        if (!oldWorkspaceIds.includes(workspaceId)) {
          simpleAuthService.assignWorkspaceToUser(userId, workspaceId);
        }
      });
    } catch (error) {
      console.error('Failed to update workspace assignments:', error);
    }
  };

  const handleDeleteUser = (user) => {
    if (window.confirm(`确定要删除用户 "${user.username}" 吗？此操作不可撤销。`)) {
      try {
        simpleAuthService.deleteUser(user.id);
        setError(null);
        setSuccess(`用户 "${user.username}" 删除成功`);
        loadUsers();
        console.log('User deleted successfully');
      } catch (error) {
        setError(error.message || '删除用户失败');
      }
    }
  };

  const getRoleIcon = (role: 'admin' | 'operator') => {
    return role === 'admin' ? <AdminPanelSettings /> : <Engineering />;
  };

  const getRoleColor = (role: 'admin' | 'operator') => {
    return role === 'admin' ? 'primary' : 'secondary';
  };

  const getRoleLabel = (role: 'admin' | 'operator') => {
    return role === 'admin' ? '管理员' : '操作员';
  };

  const getWorkspaceName = (workspaceId: string) => {
    const workspace = workspaces.find(w => w.id === workspaceId);
    return workspace ? workspace.name : `工作区-${workspaceId.slice(-4)}`;
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            用户管理
          </Typography>
          <Typography variant="body1" color="text.secondary">
            管理系统用户和权限分配
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleCreateUser}
        >
          创建用户
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* 用户统计 */}
      <Box sx={{ display: 'flex', gap: 3, mb: 4 }}>
        <Paper sx={{ p: 3, textAlign: 'center', flex: 1 }}>
          <Typography variant="h4">{users.length}</Typography>
          <Typography variant="body2" color="text.secondary">总用户数</Typography>
        </Paper>
        <Paper sx={{ p: 3, textAlign: 'center', flex: 1 }}>
          <Typography variant="h4">
            {users.filter(u => u.role === 'admin').length}
          </Typography>
          <Typography variant="body2" color="text.secondary">管理员</Typography>
        </Paper>
        <Paper sx={{ p: 3, textAlign: 'center', flex: 1 }}>
          <Typography variant="h4">
            {users.filter(u => u.role === 'operator').length}
          </Typography>
          <Typography variant="body2" color="text.secondary">操作员</Typography>
        </Paper>
      </Box>

      {/* 用户列表 */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>用户信息</TableCell>
              <TableCell>角色</TableCell>
              <TableCell>工作区权限</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id || user.username}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getRoleIcon(user.role)}
                    <Box>
                      <Typography variant="subtitle2">{user.username}</Typography>
                      {user.email && (
                        <Typography variant="caption" color="text.secondary">
                          {user.email}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getRoleLabel(user.role)}
                    color={getRoleColor(user.role)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    {user.role === 'admin' ? (
                      <Chip label="全部工作区" color="primary" size="small" />
                    ) : (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {user.workspaceIds?.length > 0 ? (
                          user.workspaceIds.slice(0, 2).map(wsId => (
                            <Chip
                              key={wsId}
                              label={getWorkspaceName(wsId)}
                              size="small"
                              variant="outlined"
                              color="primary"
                            />
                          ))
                        ) : (
                          <Typography variant="caption" color="text.secondary">
                            未分配
                          </Typography>
                        )}
                        {user.workspaceIds?.length > 2 && (
                          <Chip
                            label={`+${user.workspaceIds.length - 2}`}
                            size="small"
                            variant="outlined"
                            color="secondary"
                          />
                        )}
                      </Box>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label="活跃"
                    color="success"
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      onClick={() => handleEditUser(user)}
                      variant="outlined"
                    >
                      编辑
                    </Button>
                    <Button
                      size="small"
                      color="error"
                      onClick={() => handleDeleteUser(user)}
                      variant="outlined"
                      disabled={user.username === simpleAuthService.getCurrentUser()?.username}
                    >
                      删除
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 创建/编辑用户对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{editingUser ? '编辑用户' : '创建新用户'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="用户名"
            fullWidth
            variant="outlined"
            value={newUser.username}
            onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
            disabled={!!editingUser}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label="邮箱"
            type="email"
            fullWidth
            variant="outlined"
            value={newUser.email}
            onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label={editingUser ? "新密码（留空保持不变）" : "密码"}
            type="password"
            fullWidth
            variant="outlined"
            value={newUser.password}
            onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>角色</InputLabel>
            <Select
              value={newUser.role}
              label="角色"
              onChange={(e) => {
                const newRole = e.target.value as 'admin' | 'operator';
                setNewUser(prev => ({
                  ...prev,
                  role: newRole,
                  // 如果改为管理员，清空工作区分配
                  workspaceIds: newRole === 'admin' ? [] : prev.workspaceIds
                }));
              }}
            >
              <MenuItem value="admin">管理员</MenuItem>
              <MenuItem value="operator">操作员</MenuItem>
            </Select>
          </FormControl>

          {/* 工作区权限分配 */}
          {newUser.role === 'operator' && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                工作区权限分配
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                选择该操作员可以访问的工作区
              </Typography>
              <Box sx={{
                maxHeight: 200,
                overflow: 'auto',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: 1,
                p: 1,
                mt: 1
              }}>
                {workspaces.length > 0 ? (
                  workspaces.map((workspace) => (
                    <FormControlLabel
                      key={workspace.id}
                      control={
                        <Checkbox
                          checked={newUser.workspaceIds.includes(workspace.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setNewUser(prev => ({
                                ...prev,
                                workspaceIds: [...prev.workspaceIds, workspace.id]
                              }));
                            } else {
                              setNewUser(prev => ({
                                ...prev,
                                workspaceIds: prev.workspaceIds.filter(id => id !== workspace.id)
                              }));
                            }
                          }}
                        />
                      }
                      label={
                        <Box>
                          <Typography variant="body2">{workspace.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {workspace.namespaces?.length || 0} 个命名空间
                          </Typography>
                        </Box>
                      }
                      sx={{ display: 'block', mb: 0.5 }}
                    />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                    暂无可分配的工作区
                  </Typography>
                )}
              </Box>
              {newUser.workspaceIds.length > 0 && (
                <Typography variant="caption" color="primary" sx={{ mt: 1, display: 'block' }}>
                  已选择 {newUser.workspaceIds.length} 个工作区
                </Typography>
              )}
            </Box>
          )}

          {newUser.role === 'admin' && (
            <Alert severity="info" sx={{ mt: 2 }}>
              管理员拥有所有工作区的访问权限，无需单独分配。
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setDialogOpen(false);
            setEditingUser(null);
            setNewUser({ username: '', password: '', email: '', role: 'operator', workspaceIds: [] });
            setError(null);
          }}>取消</Button>
          <Button onClick={handleSaveUser} variant="contained">
            {editingUser ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 功能说明 */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>功能说明:</strong><br/>
          • 管理员: 可以管理所有工作区、用户和系统配置<br/>
          • 操作员: 只能访问分配给自己的工作区，进行应用管理操作<br/>
          • 当前为演示版本，实际的用户创建和编辑功能需要后端API支持
        </Typography>
      </Alert>
    </Container>
  );
};

export default SimpleUserManagement;
