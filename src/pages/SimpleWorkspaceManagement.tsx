import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Alert,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress
} from '@mui/material';
import {
  Add,
  Folder,
  Storage,
  Apps,
  Business,
  Security,
  CheckBox,
  CheckBoxOutlineBlank,
  Refresh,
  Settings
} from '@mui/icons-material';
import { RancherAPI } from '../services/rancherApi';
import { simpleAuthService } from '../services/simpleAuth';
import ConfigDialog from '../components/ConfigDialog';

interface WorkspaceConfig {
  id: string;
  name: string;
  description: string;
  namespaces: string[];
  assignedUsers: string[];
  clusterId: string;
  created: string;
  source: 'rancher' | 'local';
}

const SimpleWorkspaceManagement: React.FC = () => {
  const [workspaces, setWorkspaces] = useState<WorkspaceConfig[]>([]);
  const [availableNamespaces, setAvailableNamespaces] = useState<any[]>([]);
  const [clusters, setClusters] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingWorkspace, setEditingWorkspace] = useState<WorkspaceConfig | null>(null);
  const [newWorkspace, setNewWorkspace] = useState({
    name: '',
    description: '',
    namespaces: [] as string[],
    assignedUsers: [] as string[],
    clusterId: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [rancherApi, setRancherApi] = useState<RancherAPI | null>(null);
  const [connectionStatus, setConnectionStatus] = useState('未连接');
  const [configDialogOpen, setConfigDialogOpen] = useState(false);

  const currentUser = simpleAuthService.getCurrentUser();

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    setLoading(true);

    try {
      // 加载用户列表
      loadUsers();

      // 加载工作区配置
      loadWorkspaceConfigs();

      // 加载 Rancher 配置
      const savedConfig = localStorage.getItem('rancherConfig');
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        const api = new RancherAPI(config);
        setRancherApi(api);

        try {
          await api.testConnection();
          setConnectionStatus('已连接');
          await loadRancherData(api);
        } catch (error) {
          setConnectionStatus('连接失败');
          console.error('Rancher connection failed:', error);
          loadLocalData();
        }
      } else {
        setConnectionStatus('未配置');
        loadLocalData();
      }
    } catch (error) {
      console.error('Failed to initialize data:', error);
      setError('初始化失败: ' + error.message);
      loadLocalData();
      loadUsers();
    } finally {
      setLoading(false);
    }
  };

  const loadRancherData = async (api: RancherAPI) => {
    try {
      // 获取集群
      const clustersData = await api.getClusters();
      console.log('Loaded clusters:', clustersData);
      setClusters(clustersData);

      if (clustersData.length > 0) {
        // 获取第一个集群的命名空间
        const namespacesData = await api.getNamespaces(clustersData[0].id);
        console.log('Loaded namespaces:', namespacesData);
        setAvailableNamespaces(namespacesData);
      } else {
        console.warn('No clusters found');
        setAvailableNamespaces([]);
      }

      // 加载已保存的工作区配置
      loadWorkspaceConfigs();
    } catch (error) {
      console.error('Failed to load Rancher data:', error);
      setError(`加载 Rancher 数据失败: ${error.message}`);
      // 降级到本地数据
      loadLocalData();
    }
  };

  const loadLocalData = () => {
    // 加载本地工作区数据
    loadWorkspaceConfigs();

    // 模拟命名空间数据
    setAvailableNamespaces([
      { name: 'default', id: 'default' },
      { name: 'kube-system', id: 'kube-system' },
      { name: 'production', id: 'production' },
      { name: 'development', id: 'development' },
      { name: 'testing', id: 'testing' }
    ]);
  };

  const loadWorkspaceConfigs = () => {
    const saved = localStorage.getItem('workspace_configs');
    if (saved) {
      setWorkspaces(JSON.parse(saved));
    }
  };

  const loadUsers = () => {
    const allUsers = simpleAuthService.getUsers();
    setUsers(allUsers.filter(user => user.role === 'operator')); // 只显示操作员
  };

  const handleCreateWorkspace = () => {
    setEditingWorkspace(null);
    setNewWorkspace({
      name: '',
      description: '',
      namespaces: [],
      assignedUsers: [],
      clusterId: clusters.length > 0 ? clusters[0].id : ''
    });
    setDialogOpen(true);
  };

  const handleEditWorkspace = (workspace: WorkspaceConfig) => {
    setEditingWorkspace(workspace);
    setNewWorkspace({
      name: workspace.name,
      description: workspace.description,
      namespaces: workspace.namespaces,
      assignedUsers: workspace.assignedUsers,
      clusterId: workspace.clusterId
    });
    setDialogOpen(true);
  };

  const handleSaveWorkspace = () => {
    if (!newWorkspace.name.trim()) {
      setError('工作区名称不能为空');
      return;
    }

    if (newWorkspace.namespaces.length === 0) {
      setError('请至少选择一个命名空间');
      return;
    }

    const workspace: WorkspaceConfig = {
      id: editingWorkspace?.id || `ws-${Date.now()}`,
      name: newWorkspace.name,
      description: newWorkspace.description,
      namespaces: newWorkspace.namespaces,
      assignedUsers: newWorkspace.assignedUsers,
      clusterId: newWorkspace.clusterId,
      created: editingWorkspace?.created || new Date().toISOString(),
      source: connectionStatus === '已连接' ? 'rancher' : 'local'
    };

    let updatedWorkspaces;
    if (editingWorkspace) {
      updatedWorkspaces = workspaces.map(w => w.id === editingWorkspace.id ? workspace : w);
    } else {
      updatedWorkspaces = [...workspaces, workspace];
    }

    setWorkspaces(updatedWorkspaces);
    localStorage.setItem('workspace_configs', JSON.stringify(updatedWorkspaces));

    // 更新用户的工作区分配
    updateUserWorkspaceAssignments(workspace);

    setDialogOpen(false);
    setEditingWorkspace(null);
    setNewWorkspace({ name: '', description: '', namespaces: [], assignedUsers: [], clusterId: '' });
    setError(null);
  };

  const updateUserWorkspaceAssignments = (workspace: WorkspaceConfig) => {
    // 为分配的用户添加工作区权限
    workspace.assignedUsers.forEach(userId => {
      try {
        simpleAuthService.assignWorkspaceToUser(userId, workspace.id);
      } catch (error) {
        console.error('Failed to assign workspace to user:', error);
      }
    });

    // 移除未分配用户的工作区权限
    users.forEach(user => {
      if (!workspace.assignedUsers.includes(user.id) && user.workspaceIds.includes(workspace.id)) {
        try {
          simpleAuthService.removeWorkspaceFromUser(user.id, workspace.id);
        } catch (error) {
          console.error('Failed to remove workspace from user:', error);
        }
      }
    });
  };

  const handleDeleteWorkspace = (id: string) => {
    if (window.confirm('确定要删除这个工作区吗？这将移除所有用户对此工作区的访问权限。')) {
      const workspace = workspaces.find(w => w.id === id);

      // 移除所有用户的工作区权限
      if (workspace) {
        workspace.assignedUsers.forEach(userId => {
          try {
            simpleAuthService.removeWorkspaceFromUser(userId, id);
          } catch (error) {
            console.error('Failed to remove workspace from user:', error);
          }
        });
      }

      const updatedWorkspaces = workspaces.filter(w => w.id !== id);
      setWorkspaces(updatedWorkspaces);
      localStorage.setItem('workspace_configs', JSON.stringify(updatedWorkspaces));
    }
  };

  const handleConfigSave = (config) => {
    localStorage.setItem('rancherConfig', JSON.stringify(config));
    setConfigDialogOpen(false);
    setError(null);
    // 重新初始化数据
    initializeData();
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6">正在加载工作区数据...</Typography>
          <Typography variant="body2" color="text.secondary">
            正在连接 Rancher 并获取集群信息
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4 }}>
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            <Business sx={{ mr: 1, verticalAlign: 'middle' }} />
            工作区管理
          </Typography>
          <Typography variant="body1" color="text.secondary">
            管理工作区和命名空间权限分配
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
            <Chip
              label={`Rancher: ${connectionStatus}`}
              color={connectionStatus === '已连接' ? 'success' : connectionStatus === '连接失败' ? 'error' : 'default'}
              size="small"
            />
            {clusters.length > 0 && (
              <Chip
                label={`集群: ${clusters[0]?.name || clusters[0]?.id}`}
                size="small"
                variant="outlined"
              />
            )}
            {connectionStatus !== '已连接' && (
              <Button
                variant="outlined"
                size="small"
                startIcon={<Settings />}
                onClick={() => setConfigDialogOpen(true)}
                sx={{ ml: 1 }}
              >
                配置 Rancher
              </Button>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Refresh />}
              onClick={initializeData}
              disabled={loading}
            >
              刷新
            </Button>
          </Box>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleCreateWorkspace}
          disabled={loading}
        >
          创建工作区
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 统计信息 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Folder sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4">{workspaces.length}</Typography>
            <Typography variant="body2" color="text.secondary">工作区总数</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Storage sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
            <Typography variant="h4">
              {workspaces.reduce((sum, w) => sum + (w.namespaces?.length || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">命名空间总数</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Apps sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
            <Typography variant="h4">0</Typography>
            <Typography variant="body2" color="text.secondary">活跃应用</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* 工作区列表 */}
      <Typography variant="h5" gutterBottom>
        工作区列表
      </Typography>

      {workspaces.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Folder sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            还没有工作区
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            创建工作区来组织和管理您的 Kubernetes 命名空间
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setDialogOpen(true)}
            sx={{ mt: 2 }}
          >
            创建第一个工作区
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {workspaces.map((workspace) => (
            <Grid item xs={12} md={6} lg={4} key={workspace.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Folder sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" component="div">
                      {workspace.name}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {workspace.description}
                  </Typography>

                  <Typography variant="body2" gutterBottom>
                    命名空间 ({workspace.namespaces?.length || 0}):
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                    {workspace.namespaces?.slice(0, 3).map(ns => (
                      <Chip key={ns} label={ns} size="small" color="primary" />
                    )) || []}
                    {(workspace.namespaces?.length || 0) > 3 && (
                      <Chip label={`+${(workspace.namespaces?.length || 0) - 3}`} size="small" variant="outlined" />
                    )}
                  </Box>

                  <Typography variant="body2" gutterBottom>
                    分配用户 ({workspace.assignedUsers?.length || 0}):
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                    {workspace.assignedUsers?.slice(0, 2).map(userId => {
                      const user = users.find(u => u.id === userId);
                      return user ? (
                        <Chip key={userId} label={user.username} size="small" color="secondary" />
                      ) : null;
                    })}
                    {(workspace.assignedUsers?.length || 0) > 2 && (
                      <Chip label={`+${(workspace.assignedUsers?.length || 0) - 2}`} size="small" variant="outlined" />
                    )}
                    {(workspace.assignedUsers?.length || 0) === 0 && (
                      <Typography variant="caption" color="text.secondary">
                        未分配用户
                      </Typography>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      {workspace.created ? new Date(workspace.created).toLocaleDateString() : '未知'}
                    </Typography>
                    <Chip
                      label={workspace.source === 'rancher' ? 'Rancher' : '本地'}
                      size="small"
                      color={workspace.source === 'rancher' ? 'success' : 'default'}
                      variant="outlined"
                    />
                  </Box>
                </CardContent>
                
                <CardActions>
                  <Button
                    size="small"
                    onClick={() => handleEditWorkspace(workspace)}
                  >
                    编辑
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => handleDeleteWorkspace(workspace.id)}
                  >
                    删除
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* 创建/编辑工作区对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingWorkspace ? '编辑工作区' : '创建工作区'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="工作区名称"
            fullWidth
            variant="outlined"
            value={newWorkspace.name}
            onChange={(e) => setNewWorkspace(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label="描述"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            value={newWorkspace.description}
            onChange={(e) => setNewWorkspace(prev => ({ ...prev, description: e.target.value }))}
            sx={{ mb: 2 }}
          />

          {/* 集群选择 */}
          {clusters.length > 0 && (
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>选择集群</InputLabel>
              <Select
                value={newWorkspace.clusterId}
                label="选择集群"
                onChange={(e) => setNewWorkspace(prev => ({ ...prev, clusterId: e.target.value }))}
              >
                {clusters.map((cluster) => (
                  <MenuItem key={cluster.id} value={cluster.id}>
                    {cluster.name || cluster.id}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}

          {/* 命名空间选择 */}
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            选择命名空间
          </Typography>
          <Box sx={{ maxHeight: 200, overflow: 'auto', border: '1px solid rgba(255,255,255,0.2)', borderRadius: 1, p: 1 }}>
            {availableNamespaces.length > 0 ? (
              availableNamespaces.map((namespace) => (
                <FormControlLabel
                  key={namespace.name || namespace.id}
                  control={
                    <Checkbox
                      checked={newWorkspace.namespaces.includes(namespace.name)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewWorkspace(prev => ({
                            ...prev,
                            namespaces: [...prev.namespaces, namespace.name]
                          }));
                        } else {
                          setNewWorkspace(prev => ({
                            ...prev,
                            namespaces: prev.namespaces.filter(ns => ns !== namespace.name)
                          }));
                        }
                      }}
                    />
                  }
                  label={namespace.name}
                  sx={{ display: 'block', mb: 0.5 }}
                />
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                {connectionStatus === '已连接' ? '正在加载命名空间...' : '请先配置 Rancher 连接以获取命名空间'}
              </Typography>
            )}
          </Box>

          {/* 用户分配 */}
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            分配用户
          </Typography>
          <Box sx={{ maxHeight: 150, overflow: 'auto', border: '1px solid rgba(255,255,255,0.2)', borderRadius: 1, p: 1 }}>
            {users.map((user) => (
              <FormControlLabel
                key={user.id}
                control={
                  <Checkbox
                    checked={newWorkspace.assignedUsers.includes(user.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setNewWorkspace(prev => ({
                          ...prev,
                          assignedUsers: [...prev.assignedUsers, user.id]
                        }));
                      } else {
                        setNewWorkspace(prev => ({
                          ...prev,
                          assignedUsers: prev.assignedUsers.filter(id => id !== user.id)
                        }));
                      }
                    }}
                  />
                }
                label={`${user.username} (${user.email || '无邮箱'})`}
                sx={{ display: 'block', mb: 0.5 }}
              />
            ))}
            {users.length === 0 && (
              <Typography variant="body2" color="text.secondary">
                暂无操作员用户
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>取消</Button>
          <Button onClick={handleSaveWorkspace} variant="contained">
            {editingWorkspace ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 功能说明 */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>功能说明:</strong><br/>
          • 工作区用于组织和管理相关的 Kubernetes 命名空间<br/>
          • 每个工作区可以分配给不同的操作员进行管理<br/>
          • 连接到 Rancher API 后可以获取真实的集群和命名空间数据
        </Typography>
      </Alert>

      {/* Rancher 配置对话框 */}
      <ConfigDialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        onSave={handleConfigSave}
        initialConfig={null}
      />
    </Container>
  );
};

export default SimpleWorkspaceManagement;
