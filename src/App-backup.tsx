import React, { useState, useEffect } from 'react';
import { 
  <PERSON>ss<PERSON><PERSON><PERSON>, 
  ThemeProvider, 
  createTheme, 
  AppBar, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>po<PERSON>, 
  Button, 
  Box,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Divider
} from '@mui/material';
import { 
  Logout, 
  Person, 
  AdminPanelSettings, 
  Engineering 
} from '@mui/icons-material';
import MainDashboard from './pages/MainDashboard';
import WorkspaceManagement from './pages/WorkspaceManagement';
import UserManagement from './pages/UserManagement';
import DebugPage from './pages/DebugPage';
import LoginPage from './pages/LoginPage';
import { authService } from './services/authService';
import { Permission, hasPermission, UserRole } from './types/auth';
import type { User } from './types/auth';

const theme = createTheme({
  palette: {
    mode: 'dark',
  },
});

type PageType = 'dashboard' | 'workspace' | 'users' | 'debug';

function App() {
  const [currentPage, setCurrentPage] = useState<PageType>('dashboard');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);

  useEffect(() => {
    // 检查认证状态
    const authState = authService.getAuthState();
    setIsAuthenticated(authState.isAuthenticated);
    setCurrentUser(authState.user);
  }, []);

  const handleLoginSuccess = () => {
    const authState = authService.getAuthState();
    setIsAuthenticated(true);
    setCurrentUser(authState.user);
  };

  const handleLogout = () => {
    authService.logout();
    setIsAuthenticated(false);
    setCurrentUser(null);
    setCurrentPage('dashboard');
    setUserMenuAnchor(null);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const getRoleIcon = (role: UserRole) => {
    return role === UserRole.ADMIN ? <AdminPanelSettings /> : <Engineering />;
  };

  const getRoleLabel = (role: UserRole) => {
    return role === UserRole.ADMIN ? '管理员' : '操作员';
  };

  // 如果未认证，显示登录页面
  if (!isAuthenticated) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <LoginPage onLoginSuccess={handleLoginSuccess} />
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            KubeX 管理平台
          </Typography>
          
          {/* 导航按钮 */}
          <Button 
            color="inherit" 
            onClick={() => setCurrentPage('dashboard')}
            variant={currentPage === 'dashboard' ? 'outlined' : 'text'}
            sx={{ mr: 1 }}
          >
            操作面板
          </Button>
          
          {/* 工作区管理 - 只有管理员可见 */}
          {hasPermission(currentUser, Permission.VIEW_ALL_WORKSPACES) && (
            <Button 
              color="inherit" 
              onClick={() => setCurrentPage('workspace')}
              variant={currentPage === 'workspace' ? 'outlined' : 'text'}
              sx={{ mr: 1 }}
            >
              工作区管理
            </Button>
          )}
          
          {/* 用户管理 - 只有管理员可见 */}
          {hasPermission(currentUser, Permission.MANAGE_USERS) && (
            <Button 
              color="inherit" 
              onClick={() => setCurrentPage('users')}
              variant={currentPage === 'users' ? 'outlined' : 'text'}
              sx={{ mr: 1 }}
            >
              用户管理
            </Button>
          )}
          
          <Button 
            color="inherit" 
            onClick={() => setCurrentPage('debug')}
            variant={currentPage === 'debug' ? 'outlined' : 'text'}
            sx={{ mr: 2 }}
          >
            调试工具
          </Button>

          {/* 用户菜单 */}
          <IconButton
            size="large"
            onClick={handleUserMenuOpen}
            color="inherit"
          >
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              {currentUser?.username.charAt(0).toUpperCase()}
            </Avatar>
          </IconButton>
          
          <Menu
            anchorEl={userMenuAnchor}
            open={Boolean(userMenuAnchor)}
            onClose={handleUserMenuClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <MenuItem disabled>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {currentUser && getRoleIcon(currentUser.role)}
                <Box>
                  <Typography variant="subtitle2">{currentUser?.username}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {currentUser && getRoleLabel(currentUser.role)}
                  </Typography>
                </Box>
              </Box>
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <Logout sx={{ mr: 1 }} />
              退出登录
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      
      <Box sx={{ minHeight: '100vh' }}>
        {currentPage === 'dashboard' && <MainDashboard />}
        {currentPage === 'workspace' && <WorkspaceManagement />}
        {currentPage === 'users' && <UserManagement />}
        {currentPage === 'debug' && <DebugPage />}
      </Box>
    </ThemeProvider>
  );
}

export default App;
