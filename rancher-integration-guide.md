# Rancher API 对接总结文档

## 项目概述

本文档总结了在统一数字资源管理平台中对接Rancher API的完整过程，包括技术实现、关键发现、最佳实践和注意事项。

## 技术架构

### 核心组件
- **前端框架**: React + TypeScript + Material-UI
- **API服务**: Rancher REST API v2
- **认证方式**: Bearer Token
- **状态管理**: React Hooks (useState, useEffect)

### 文件结构
```
src/
├── services/
│   └── rancherApi.ts          # Rancher API 封装
├── pages/
│   ├── KubernetesResources.tsx   # 主要管理界面
│   └── KubernetesDebug.tsx       # 调试界面
└── router/
    └── index.tsx              # 路由配置
```

## 关键技术发现

### 1. API 端点映射

#### 集群ID映射
```typescript
// 重要发现：Rancher UI显示的集群ID与API实际使用的不同
const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
```

#### 核心API端点
```typescript
// 基础URL构建
const baseUrl = 'https://rancher.local/rancher-api'

// 关键端点
const endpoints = {
  clusters: '/v3/clusters',
  deployments: '/k8s/clusters/{clusterId}/apis/apps/v1/namespaces/{namespace}/deployments',
  namespaces: '/k8s/clusters/{clusterId}/api/v1/namespaces',
  scale: '/k8s/clusters/{clusterId}/apis/apps/v1/namespaces/{namespace}/deployments/{name}/scale'
}
```

### 2. 认证机制

#### Token获取方式
1. 登录Rancher管理界面
2. 用户头像 → API & Keys
3. Add Key → 选择作用域（建议No Scope）
4. 复制生成的Token

#### 认证实现
```typescript
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
```

### 3. CSRF处理

#### 重要发现
- Rancher API使用Bearer Token认证，**不需要传统的CSRF Token**
- 直接使用Bearer Token即可完成所有操作
- 避免了复杂的CSRF Token获取和管理

## 核心功能实现

### 1. 连接测试
```typescript
async testConnection(): Promise<boolean> {
  try {
    const response = await fetch(`${this.baseUrl}/v3/clusters`, {
      headers: await this.getHeaders(false)
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}
```

### 2. 应用部署
```typescript
async deployApplication(clusterId: string, namespace: string, manifest: any): Promise<boolean> {
  const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
  const url = this.buildUrl(`k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments`, '');
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
    },
    body: JSON.stringify(manifest),
  });
  
  return response.ok;
}
```

### 3. 应用扩缩容
```typescript
async scaleDeployment(clusterId: string, namespace: string, deploymentName: string, replicas: number): Promise<boolean> {
  const actualClusterId = clusterId === 'c-m-local' ? 'local' : clusterId;
  const url = this.buildUrl(`k8s/clusters/${actualClusterId}/apis/apps/v1/namespaces/${namespace}/deployments/${deploymentName}/scale`, '');
  
  const scaleManifest = {
    apiVersion: 'autoscaling/v1',
    kind: 'Scale',
    metadata: { name: deploymentName, namespace: namespace },
    spec: { replicas: replicas }
  };

  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
    },
    body: JSON.stringify(scaleManifest),
  });

  return response.ok;
}
```

### 4. 应用重启
```typescript
async restartDeployment(clusterId: string, namespace: string, deploymentName: string): Promise<boolean> {
  // 1. 获取当前deployment
  const deployment = await this.getDeployment(clusterId, namespace, deploymentName);
  
  // 2. 添加重启annotation
  if (!deployment.spec.template.metadata.annotations) {
    deployment.spec.template.metadata.annotations = {};
  }
  deployment.spec.template.metadata.annotations['kubectl.kubernetes.io/restartedAt'] = new Date().toISOString();

  // 3. 更新deployment
  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
    },
    body: JSON.stringify(deployment),
  });

  return response.ok;
}
```

## 用户界面设计

### 1. 配置管理
- **API Token持久化存储**: localStorage自动保存
- **连接状态实时显示**: 成功/失败状态反馈
- **集群自动选择**: 连接成功后自动选择可用集群

### 2. 应用管理界面
- **紧凑卡片设计**: 高信息密度，适合显示多个应用
- **实时状态监控**: 副本数、运行状态实时更新
- **一键操作**: 部署、扩缩容、重启、删除
- **自动刷新**: 可选的10秒自动刷新功能

### 3. 响应式设计
- **宽度适配**: 95%-98%页面宽度，充分利用屏幕空间
- **多屏幕支持**: 从手机到超宽屏的完美适配
- **信息分层**: 重要信息突出，次要信息适当弱化

## 最佳实践

### 1. 错误处理
```typescript
try {
  const response = await fetch(url, options);
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${errorText}`);
  }
  return await response.json();
} catch (error) {
  console.error('API调用失败:', error);
  throw error;
}
```

### 2. 配置管理
```typescript
// 配置接口定义
interface RancherConfig {
  baseUrl: string;
  token: string;
  clusterId: string;
  savedAt?: string;
}

// 配置持久化
const saveConfig = (config: RancherConfig) => {
  const configToSave = {
    ...config,
    savedAt: new Date().toISOString()
  };
  localStorage.setItem('rancherConfig', JSON.stringify(configToSave));
};
```

### 3. 状态管理
```typescript
// 使用React Hooks管理状态
const [isConnected, setIsConnected] = useState(false);
const [deployments, setDeployments] = useState([]);
const [autoRefresh, setAutoRefresh] = useState(false);

// 自动刷新实现
useEffect(() => {
  if (!autoRefresh || !isConnected) return;
  
  const interval = setInterval(() => {
    loadDeployments();
  }, 10000);
  
  return () => clearInterval(interval);
}, [autoRefresh, isConnected]);
```

## 调试工具

### 1. 调试页面
- **专门的调试界面**: `/kubernetes/debug`
- **API测试功能**: 直接测试各种API调用
- **详细日志输出**: 便于问题排查

### 2. 日志记录
```typescript
console.log('RancherAPI: 操作类型', { 参数详情 });
console.error('RancherAPI: 错误信息', error);
```

## 注意事项

### 1. 安全考虑
- **Token安全**: 仅存储在localStorage，不传输到后端
- **HTTPS要求**: 生产环境必须使用HTTPS
- **Token权限**: 建议使用最小权限原则

### 2. 性能优化
- **请求缓存**: 避免频繁重复请求
- **分页加载**: 大量数据时考虑分页
- **错误重试**: 网络错误时的重试机制

### 3. 兼容性
- **集群ID映射**: 注意UI显示ID与API实际ID的差异
- **API版本**: 确保使用正确的API版本
- **浏览器兼容**: 现代浏览器的fetch API支持

## 未来扩展方向

### 1. 功能扩展
- **Pod日志查看**: 实时查看容器日志
- **资源监控**: CPU、内存使用率监控
- **事件查看**: Kubernetes事件流
- **配置管理**: ConfigMap和Secret管理

### 2. 技术优化
- **WebSocket连接**: 实时状态更新
- **缓存策略**: 减少API调用频率
- **批量操作**: 支持多应用批量管理
- **权限控制**: 基于角色的访问控制

## 总结

通过本次Rancher API对接，我们成功实现了：

1. **完整的Kubernetes应用生命周期管理**
2. **用户友好的管理界面**
3. **稳定可靠的API集成**
4. **高效的用户体验**

这个实现为后续的容器管理功能奠定了坚实的基础，可以作为其他Kubernetes管理工具集成的参考模板。

## 附录：完整代码示例

### A. RancherAPI 核心类结构

```typescript
class RancherAPI {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl.replace(/\/+$/, '');
    this.token = token;
  }

  private buildUrl(path: string, prefix: string = '/rancher-api'): string {
    const cleanPath = path.replace(/^\/+/, '');
    return `${this.baseUrl}${prefix}/${cleanPath}`;
  }

  private async getHeaders(includeCsrf: boolean = false): Promise<Record<string, string>> {
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
    };
  }
}
```

### B. 部署清单模板

```typescript
const createDeploymentManifest = (name: string, image: string, replicas: number) => ({
  apiVersion: 'apps/v1',
  kind: 'Deployment',
  metadata: {
    name: name,
    labels: { app: name }
  },
  spec: {
    replicas: replicas,
    selector: { matchLabels: { app: name } },
    template: {
      metadata: { labels: { app: name } },
      spec: {
        containers: [{
          name: name,
          image: image,
          ports: [{ containerPort: 80 }],
          resources: {
            requests: { memory: '64Mi', cpu: '250m' },
            limits: { memory: '128Mi', cpu: '500m' }
          }
        }]
      }
    }
  }
});
```

### C. 错误处理最佳实践

```typescript
const handleApiError = (error: any, operation: string) => {
  console.error(`${operation} 失败:`, error);

  if (error.response) {
    // HTTP错误响应
    const status = error.response.status;
    const message = error.response.data?.message || error.message;

    switch (status) {
      case 401:
        return '认证失败，请检查API Token';
      case 403:
        return '权限不足，请检查Token权限';
      case 404:
        return '资源不存在';
      case 500:
        return 'Rancher服务器内部错误';
      default:
        return `操作失败: ${message}`;
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查Rancher服务器地址';
  } else {
    // 其他错误
    return `操作失败: ${error.message}`;
  }
};
```

### D. 状态轮询实现

```typescript
const usePolling = (callback: () => void, interval: number, enabled: boolean) => {
  useEffect(() => {
    if (!enabled) return;

    const timer = setInterval(callback, interval);
    return () => clearInterval(timer);
  }, [callback, interval, enabled]);
};

// 使用示例
usePolling(() => {
  if (isConnected && rancherConfig.clusterId) {
    loadDeployments();
  }
}, 10000, autoRefresh);
```

## 故障排除指南

### 常见问题及解决方案

#### 1. 连接失败
**问题**: 无法连接到Rancher服务器
**解决方案**:
- 检查Rancher服务器地址是否正确
- 确认网络连通性
- 验证HTTPS证书（如果使用自签名证书）

#### 2. 认证失败
**问题**: API Token认证失败
**解决方案**:
- 重新生成API Token
- 检查Token是否过期
- 确认Token权限范围

#### 3. 集群ID错误
**问题**: 找不到指定集群
**解决方案**:
- 使用正确的集群ID映射（c-m-local → local）
- 检查集群状态是否正常
- 确认用户对集群的访问权限

#### 4. 部署失败
**问题**: 应用部署失败
**解决方案**:
- 检查镜像地址是否正确
- 验证资源配额是否充足
- 确认命名空间是否存在

### 调试技巧

1. **开启详细日志**: 在浏览器开发者工具中查看网络请求
2. **使用调试页面**: 访问 `/kubernetes/debug` 进行API测试
3. **检查Rancher日志**: 在Rancher服务器端查看相关日志
4. **验证权限**: 确保API Token具有足够的权限

## 性能优化建议

### 1. 请求优化
- 使用适当的缓存策略
- 避免频繁的API调用
- 实现请求去重机制

### 2. 界面优化
- 使用虚拟滚动处理大量数据
- 实现懒加载减少初始加载时间
- 优化重新渲染性能

### 3. 网络优化
- 实现请求重试机制
- 使用连接池管理
- 考虑使用WebSocket进行实时更新

---

**文档版本**: v1.0
**创建日期**: 2025-07-01
**最后更新**: 2025-07-01
**维护者**: 开发团队
